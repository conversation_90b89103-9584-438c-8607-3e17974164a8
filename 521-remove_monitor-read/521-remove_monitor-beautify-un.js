/*
 * VSCode Augment 插件反混淆分析
 *
 * 这是一个VSCode的Augment AI编程助手插件的核心文件
 * 经过混淆压缩，以下是反混淆分析结果
 */

// ============================================================================
// 核心工具函数和模块系统
// ============================================================================

// 对象创建和属性操作的简化引用
var createObject = Object.create,                    // zOt -> createObject
    defineProperty = Object.defineProperty,          // PB -> defineProperty
    getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, // MOt -> getOwnPropertyDescriptor
    getOwnPropertyNames = Object.getOwnPropertyNames,           // FOt -> getOwnPropertyNames
    getPrototypeOf = Object.getPrototypeOf,                    // NOt -> getPrototypeOf
    hasOwnProperty = Object.prototype.hasOwnProperty,          // YOt -> hasOwnProperty

    // 模块缓存和延迟加载系统
    lazyEvaluator = (moduleFunc, cachedResult) => () => cachedResult = moduleFunc ? moduleFunc(moduleFunc = 0) : cachedResult, // ec -> lazyEvaluator

    // CommonJS模块包装器 - 创建模块导出对象
    moduleWrapper = (moduleFunc, exports) => () => (exports || moduleFunc((exports = {exports: {}}).exports, exports), exports.exports), // O -> moduleWrapper

    // 属性批量定义工具
    defineProperties = (target, properties) => {     // Yy -> defineProperties
        for (var key in properties) defineProperty(target, key, {
            get: properties[key],
            enumerable: true
        })
    },

    // 对象属性复制工具 - 用于模块导入/导出
    copyProperties = (target, source, excludeKey, descriptor) => {  // P7e -> copyProperties
        if (source && "object" == typeof source || "function" == typeof source)
            for (let key of getOwnPropertyNames(source))
                hasOwnProperty.call(target, key) || key === excludeKey ||
                defineProperty(target, key, {
                    get: () => source[key],
                    enumerable: !(descriptor = getOwnPropertyDescriptor(source, key)) || descriptor.enumerable
                });
        return target
    },

    // ES模块导入包装器
    moduleImporter = (module, isDefault, result) => (  // me -> moduleImporter
        result = null != module ? createObject(getPrototypeOf(module)) : {},
        copyProperties(!isDefault && module && module.__esModule ? result : defineProperty(result, "default", {
            value: module,
            enumerable: true
        }), module)
    ),

    // ES模块导出标记
    markAsESModule = module => copyProperties(defineProperty({}, "__esModule", {  // tc -> markAsESModule
        value: true
    }), module);

// ============================================================================
// Lodash工具函数模块
// ============================================================================

// 检查是否为对象类型
var isObject = moduleWrapper((exports, module) => {  // Eh -> isObject
    module.exports = function(value) {
        var type = typeof value;
        return null != value && ("object" == type || "function" == type)
    }
});

// 全局对象检测
var getGlobalObject = moduleWrapper((exports, module) => {  // Mre -> getGlobalObject
    var globalRef = "object" == typeof global && global && global.Object === Object && global;
    module.exports = globalRef
});

// 根对象获取 (global/self/window)
var getRootObject = moduleWrapper((exports, module) => {  // mp -> getRootObject
    var globalObj = getGlobalObject(),
        selfObj = "object" == typeof self && self && self.Object === Object && self,
        root = globalObj || selfObj || Function("return this")();
    module.exports = root
});

// 当前时间戳获取器
var getCurrentTimestamp = moduleWrapper((exports, module) => {  // owe -> getCurrentTimestamp
    var root = getRootObject();
    module.exports = function() {
        return root.Date.now()
    }
});

// 字符串尾部空白字符处理
var trimEndWhitespace = moduleWrapper((exports, module) => {  // lwe -> trimEndWhitespace
    var whitespaceRegex = /\s/;
    module.exports = function(string) {
        for (var index = string.length; index-- && whitespaceRegex.test(string.charAt(index)););
        return index
    }
});

// 字符串去除首尾空白
var trimString = moduleWrapper((exports, module) => {  // dwe -> trimString
    var trimEnd = trimEndWhitespace(),
        leadingWhitespace = /^\s+/;
    module.exports = function(string) {
        return string && string.slice(0, trimEnd(string) + 1).replace(leadingWhitespace, "")
    }
});

// Symbol引用获取
var getSymbol = moduleWrapper((exports, module) => {  // Uy -> getSymbol
    var symbolRef = getRootObject().Symbol;
    module.exports = symbolRef
});

// ============================================================================
// 类型检测和转换工具
// ============================================================================

// 获取对象的字符串标签 (用于类型检测)
var getObjectTag = moduleWrapper((exports, module) => {  // mwe -> getObjectTag
    var Symbol = getSymbol(),
        objectProto = Object.prototype,
        hasOwnProp = objectProto.hasOwnProperty,
        toString = objectProto.toString,
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        var isOwn = hasOwnProp.call(value, toStringTag),
            tag = value[toStringTag];
        try {
            var unmasked = !(value[toStringTag] = void 0)
        } catch {}
        var result = toString.call(value);
        return unmasked && (isOwn ? value[toStringTag] = tag : delete value[toStringTag]), result
    }
});

// 原生toString方法
var nativeToString = moduleWrapper((exports, module) => {  // Awe -> nativeToString
    var toString = Object.prototype.toString;
    module.exports = function(value) {
        return toString.call(value)
    }
});

// 基础类型标签获取器
var getBaseTag = moduleWrapper((exports, module) => {  // SA -> getBaseTag
    var Symbol = getSymbol(),
        getTag = getObjectTag(),
        nativeObjectToString = nativeToString(),
        toStringTag = Symbol ? Symbol.toStringTag : void 0;

    module.exports = function(value) {
        return null == value ?
            void 0 === value ? "[object Undefined]" : "[object Null]" :
            (toStringTag && toStringTag in Object(value) ? getTag : nativeObjectToString)(value)
    }
});

// ============================================================================
// 插件主要功能模块
// ============================================================================

/*
 * 从代码结构分析，这个插件主要包含以下核心功能：
 *
 * 1. AI代码补全和建议 (Augment AI completion)
 * 2. 聊天界面和交互 (Chat interface)
 * 3. 工作区管理 (Workspace management)
 * 4. 用户认证和会话管理 (Authentication & session)
 * 5. 配置管理 (Configuration management)
 * 6. 特性标志管理 (Feature flags)
 * 7. 文件编辑和同步 (File editing & sync)
 * 8. 模型管理 (AI model management)
 * 9. 调试和诊断功能 (Debug & diagnostics)
 * 10. 扩展生命周期管理 (Extension lifecycle)
 */

// 从文件末尾可以看到主要的激活函数和配置同步
// 主激活函数: qun (应该是 "activate" 的混淆名)
// 配置同步函数: fkt (应该是 "setupContextKeySync" 的混淆名)

// ============================================================================
// 关键配置项和上下文键
// ============================================================================

/*
 * 从代码中识别出的重要配置项：
 *
 * - vscode-augment.enableDebugFeatures: 启用调试功能
 * - vscode-augment.enableReviewerWorkflows: 启用审查工作流
 * - vscode-augment.enableNextEdit: 启用下一步编辑功能
 * - vscode-augment.enableGenerateCommitMessage: 启用生成提交消息
 * - vscode-augment.workspace-manager-ui.enabled: 工作区管理UI
 * - vscode-augment.sources-enabled: 启用源码功能
 * - vscode-augment.chat-hint.decoration: 聊天提示装饰
 * - vscode-augment.cpu-profile.enabled: CPU性能分析
 * - vscode-augment.featureFlags.enableRemoteAgents: 远程代理
 */

// ============================================================================
// URI处理和命令路由
// ============================================================================

/*
 * 插件支持的URI路径：
 *
 * - /auth/mcp: MCP OAuth回调处理
 * - S.authRedirectURI.path: 认证重定向
 * - zte.openChat: 打开聊天界面
 * - zte.openAugmentSettings: 打开Augment设置
 * - zte.openGuidelinesSettings: 打开指导原则设置
 * - zte.openMemories: 打开记忆文件
 */

// ============================================================================
// 核心类和主要功能模块分析
// ============================================================================

/*
 * 通过分析代码结构，发现这个插件的主要类和功能模块：
 */

// 主要的类定义 (从class关键字识别)
// 1. PS - 路径迭代器类，用于遍历文件系统路径
// 2. p8e - 不支持的文件扩展名错误类
// 3. nte - 文件扩展名过滤器基类
// 4. f8e - 带忽略路径映射的文件过滤器
// 5. h8e - 带忽略栈的文件过滤器
// 6. ite - 序列号生成器类
// 7. m8e - Blob名称计算器类
// 8. _8e - 文件变更监听器类
// 9. A8e - 文件上传处理器类
// 10. b8e - 可上传文件判断器类

// 工作区和版本控制相关类
// 11. cte - Git操作类，处理工作目录变更
// 12. lte - VCS仓库监听器，监控版本控制系统变更
// 13. ute - 仓库监听器管理器
// 14. dte - 开放文件管理器代理

// 数据存储和缓存相关类
// 15. pte - 简单键值存储类
// 16. fte - Blob状态存储类
// 17. C8e - 开放文档快照缓存类
// 18. hte - 文件编辑处理器类
// 19. x8e - 工作区资源管理器
// 20. k8e - 文件变更大小计数器

// 文件管理和同步相关类
// 21. mte - 开放文件管理器V2
// 22. KW - 工作队列项失败错误类
// 23. T8e - 错误抛出器类
// 24. R8e - 进度报告器类
// 25. D8e - 单项工作队列项
// 26. L8e - 批量工作队列项
// 27. _te - 基础队列类
// 28. O8e - 单项处理队列
// 29. W8e - 批量处理队列
// 30. Ate - 工作队列管理器

// 文件系统和路径管理
// 31. bte - 检查点管理器
// 32. B8e - 限制容器类
// 33. z8e - 项目计数限制器
// 34. vte - 磁盘文件管理器
// 35. T3 - 缓存文件名常量类
// 36. M8e - 修改时间缓存条目
// 37. yte - 修改时间缓存管理器

// 变更跟踪和文档管理
// 38. F8e - 文件修改记录类
// 39. QS - 变更序列类
// 40. qS - 变更跟踪器类
// 41. wte - 跟踪文档基类
// 42. N8e - 文本文档跟踪类
// 43. Y8e - 笔记本文档跟踪类
// 44. Cte - 开放文件管理器V1

// 路径和文件分类
// 45. Ste - 文件分类器类
// 46. kte - 路径映射管理器
// 47. P8e - 路径信息管理器
// 48. $8e - 路径错误类
// 49. Ite - 路径通知器类

// 工作区和同步管理
// 50. Tte - 文件验证器类
// 51. Rte - 固定大小列表类
// 52. Dte - 标签页监听器类
// 53. Lte - 未知Blob处理器类
// 54. Ote - 查看内容跟踪器类

// 核心工作区管理
// 55. US - 工作区状态类
// 56. V8e - 文件夹资源管理器
// 57. H8e - 路径过滤器基类
// 58. Wte - 工作区管理器主类 (核心类)
// 59. Bte - 动作模型状态管理器

// 主要的扩展类
// 60. U9 - 主扩展类 (核心扩展管理器)

// ============================================================================
// 主要功能函数分析
// ============================================================================

/*
 * 关键功能函数：
 */

// 工具函数
// - mx(): 生成随机字节数组，用于UUID生成
// - aBt(): UUID验证函数
// - kA(): UUID格式化函数
// - oBt(): UUID字符串化验证函数
// - cBt(): UUID v1生成函数
// - lBt(): UUID解析函数
// - uBt(): 字符串转字节数组
// - bx(): UUID命名空间处理函数
// - fBt(): MD5哈希函数
// - gBt(): UUID v4生成函数
// - mBt(): SHA1哈希函数
// - ABt(): UUID版本获取函数

// 类型系统和装饰器
// - FCe(): 类继承扩展函数
// - NCe(): 对象属性排除函数
// - YCe(): 装饰器应用函数
// - PCe(): 参数装饰器函数
// - RBt(): ES装饰器函数
// - DBt(): 初始化器运行函数
// - LBt(): 属性键处理函数
// - OBt(): 函数名设置函数

// 异步和生成器
// - $Ce(): 元数据装饰器函数
// - QCe(): 异步等待器函数
// - qCe(): 生成器函数
// - UCe(): 导出星号函数
// - pz(): 值迭代器函数
// - lne(): 读取函数
// - VCe(): 展开函数
// - HCe(): 展开数组函数
// - GCe(): 展开数组函数
// - Jy(): 等待包装器函数
// - jCe(): 异步生成器函数
// - KCe(): 异步委托器函数
// - JCe(): 异步值函数
// - XCe(): 模板对象创建函数
// - ZCe(): 导入星号函数
// - eSe(): 默认导入函数

// 私有成员访问
// - tSe(): 私有字段获取函数
// - rSe(): 私有字段设置函数
// - nSe(): 私有字段检查函数
// - sSe(): 可释放资源添加函数
// - iSe(): 资源释放函数

// 文件系统操作
// - Sy(): 事件等待函数
// - Eun(): 对象值查找函数
// - wun(): 差异变更类型获取函数
// - Cun(): 变更类型映射函数
// - Sun(): 删除路径处理函数
// - xun(): 添加路径处理函数
// - ote(): Git差异解析函数

// 路径和文件处理
// - Gxt(): 文件管理器版本检查函数
// - gte(): 文档文本获取函数
// - S8e(): 事件文档获取函数
// - $S(): 数组过滤函数
// - lA(): 日志输出函数
// - Dun(): 修改时间条目验证函数
// - Lun(): 缓存文件名生成函数
// - Ete(): 缓存存在检查函数
// - Jxt(): 缓存移动函数
// - Xxt(): 缓存读取函数

// 文档和变更跟踪
// - Yun(): 笔记本检查函数
// - Pun(): 文档包装函数
// - U8e(): URI路径获取函数
// - tB(): 文件URI路径获取函数
// - rB(): 同步阻塞检查函数
// - akt(): 嵌套检查函数
// - np(): 文件夹状态获取函数
// - skt(): 路径扫描函数

// 主要入口函数
// - j8e(): 会话ID获取/生成函数
// - qun(): 扩展激活主函数 (插件入口点)
// - ckt(): 上下文键设置函数
// - fkt(): 上下文键同步设置函数

// ============================================================================
// 插件架构和工作流程分析
// ============================================================================

/*
 * 插件的整体架构：
 *
 * 1. 扩展激活流程 (qun函数)：
 *    - 初始化日志记录器
 *    - 设置扩展启用/禁用处理器
 *    - 注册URI处理器 (OAuth回调、聊天打开等)
 *    - 创建核心服务实例
 *
 * 2. 核心服务组件：
 *    - 认证管理 (OAuth/API Token)
 *    - 配置管理 (用户设置)
 *    - 工作区管理 (文件同步、版本控制)
 *    - AI服务 (代码补全、聊天)
 *    - 特性标志管理
 *    - 事件报告和指标收集
 *
 * 3. 文件系统集成：
 *    - 文件监听和变更跟踪
 *    - 文件上传和同步
 *    - 路径过滤和忽略规则
 *    - Git集成和版本控制
 *
 * 4. UI集成：
 *    - WebView面板 (聊天界面)
 *    - 状态栏显示
 *    - 命令注册
 *    - 上下文菜单
 *
 * 5. AI功能：
 *    - 代码补全建议
 *    - 智能聊天对话
 *    - 代码编辑建议
 *    - 提交消息生成
 */

// ============================================================================
// 重要配置项和特性标志
// ============================================================================

/*
 * 从代码中提取的重要配置项：
 *
 * 基础功能配置：
 * - enableDebugFeatures: 启用调试功能
 * - enableReviewerWorkflows: 启用代码审查工作流
 * - enableUpload: 启用文件上传功能
 * - modelName: AI模型名称配置
 * - completionURL: 代码补全服务URL
 * - apiToken: API访问令牌
 * - oauth: OAuth认证配置
 *
 * 高级功能配置：
 * - enableNextEdit: 启用下一步编辑功能
 * - enableGenerateCommitMessage: 启用提交消息生成
 * - enableSmartPaste: 启用智能粘贴
 * - enableInstructions: 启用指令功能
 * - enableWorkspaceManagerUi: 启用工作区管理UI
 *
 * 同步和上传配置：
 * - maxUploadSizeBytes: 最大上传文件大小
 * - maxTrackableFiles: 最大可跟踪文件数
 * - enableFileLimitsForSyncingPermission: 启用同步权限文件限制
 * - refuseToSyncHomeDirectories: 拒绝同步主目录
 * - verifyFolderIsSourceRepo: 验证文件夹是源代码仓库
 *
 * 性能和限制配置：
 * - vscodeEnableCpuProfile: 启用CPU性能分析
 * - bypassLanguageFilter: 绕过语言过滤器
 * - enableCompletionFileEditEvents: 启用补全文件编辑事件
 *
 * 版本控制配置：
 * - vscodeSourcesMinVersion: VSCode源码最小版本
 * - vscodeChatHintDecorationMinVersion: 聊天提示装饰最小版本
 * - vscodeNextEditMinVersion: 下一步编辑最小版本
 * - vscodeGenerateCommitMessageMinVersion: 生成提交消息最小版本
 * - vscodeBackgroundAgentsMinVersion: 后台代理最小版本
 */

// ============================================================================
// 主要激活函数 qun() 详细分析
// ============================================================================

/*
 * qun() 函数是整个插件的入口点，相当于 activate() 函数
 *
 * 函数签名：function qun(r)
 * 参数 r: VSCode扩展上下文 (ExtensionContext)
 *
 * 主要执行流程：
 */

// 1. 初始化阶段
// - 创建日志记录器: let a = Ie("activate()")
// - 输出激活日志: a.debug("======== Activating extension ========")
// - 定义内部函数：
//   * e(e): 启用扩展函数 -> e.enable()
//   * t(e): 禁用扩展函数 -> e.disable()
//   * i(): 重新加载扩展函数 -> t(s), e(s)

// 2. 生命周期管理
// - 注册扩展销毁处理器: r.subscriptions.push(new Bt.Disposable(...))
// - 在扩展销毁时调用 t(s) 禁用扩展并清理资源

// 3. URI处理器注册
// - 注册URI处理器: Bt.window.registerUriHandler({handleUri(e) {...}})
// - 处理的URI路径包括：
//   * /auth/mcp: MCP OAuth回调处理
//   * S.authRedirectURI.path: 认证重定向URI
//   * zte.openChat: 打开聊天界面
//   * zte.openAugmentSettings: 打开Augment设置
//   * zte.openGuidelinesSettings: 打开指导原则设置
//   * zte.openMemories: 打开记忆文件

// 4. 系统信息收集
// - 收集平台信息: Mte.default.platform(), arch(), release()
// - 构建用户代理字符串: 包含扩展ID、版本、平台信息、VSCode版本

// 5. 核心服务初始化
// - 创建全局状态管理器: o = new JV(r)
// - 生成会话ID: l = j8e(o)
// - 创建配置监听器: c = new fH
// - 迁移旧配置: c.migrateLegacyConfig()
// - 创建认证管理器: u = new yH(r, c)
// - 创建同步启用跟踪器: d = new Vee

// 6. API和网络服务初始化
// - 创建API服务器: p = new dH(c, u, l, n, global.fetch)
// - 创建各种缓存和事件发射器:
//   * h = new kZ (完成缓存)
//   * f = new rA(10) (最近完成)
//   * g = new rA(10) (最近指令)
//   * m = new rA(10) (最近下一步编辑结果)
//   * _ = new Bt.EventEmitter (下一步编辑WebView事件)
//   * A = new Bt.EventEmitter (扩展更新事件)
//   * v = new Bt.EventEmitter (WebView应用变更事件)
//   * y = new Bt.EventEmitter (聊天扩展事件)

// 7. 工具和资源管理器初始化
// - 创建资产管理器: b = new fK(r)
// - 创建Git操作服务: w = new FN(new m8({defaultBackupDir: r.storageUri?.fsPath}))
// - 创建主面板提供器: E = new Nee(r.extensionUri)
// - 设置可见性变更处理: E.onVisibilityChange(...)

// 8. 状态和指标管理
// - 创建入职会话事件报告器: C = new BZ(o)
// - 创建客户端指标报告器: k = new JZ(p)
// - 创建工具配置存储: S = new tK(r, c, p, u, k)
// - 创建登录应用: I = new FZ(p, c, S, C)

// 9. 状态处理函数定义
// - O(e): 处理状态变更的函数
//   * "UserShouldSignIn": 用户需要登录
//   * "WorkspaceNotSelected": 工作区未选择
//   * "ShouldDisableCopilot": 应该禁用Copilot
//   * "ShouldDisableCodeium": 应该禁用Codium
//   * "SyncingPermissionNeeded": 需要同步权限
//   * "uploadingHomeDir": 正在上传主目录
//   * "workspaceTooLarge": 工作区太大

// - W(e): 焦点管理函数
//   * 如果未禁用自动焦点且面板不可见，则执行打开命令

// 10. 事件监听器注册
// - 会话变更监听: u.onDidChangeSession(() => i())
// - 状态管理器: new bH(C, u, c)
// - WebView提供器注册: Bt.window.registerWebviewViewProvider("augment-chat", E, ...)

// 11. 主扩展实例创建
// - 创建主扩展实例: s = new U9(r, o, c, p, u, h, f, m, g, _, y, E, v, C, d, A, k, b, w)
// - 这是整个插件的核心管理器，包含所有主要功能模块

// 12. 配置变更监听
// - 监听配置变更: c.onDidChange(e => {...})
// - 当关键配置项变更时重新加载扩展:
//   * apiToken: API令牌
//   * completionURL: 补全服务URL
//   * oauth: OAuth配置
//   * modelName: 模型名称

// 13. 最终注册和启用
// - 注册文本文档内容提供器: Bt.workspace.registerTextDocumentContentProvider(U9.contentScheme, s)
// - 执行额外初始化: Dft(r), fkt(s, c, r)
// - 创建并注册命令处理器: n = kCt(...)
// - 注册各种处理器: AH.register(r, c), mH.register(r, c, s)
// - 最终启用扩展: e(s)

// ============================================================================
// 核心扩展类 U9 分析
// ============================================================================

/*
 * U9 类是整个插件的核心管理器类，包含所有主要功能模块
 *
 * 构造函数参数分析：
 * - r: 扩展上下文 (ExtensionContext)
 * - o: 全局状态管理器 (GlobalState)
 * - c: 配置监听器 (ConfigListener)
 * - p: API服务器 (ApiServer)
 * - u: 认证管理器 (AuthManager)
 * - h: 完成缓存 (CompletionCache)
 * - f: 最近完成 (RecentCompletions)
 * - m: 最近指令 (RecentInstructions)
 * - g: 最近下一步编辑结果 (RecentNextEditResults)
 * - _: 下一步编辑WebView事件 (NextEditWebViewEvent)
 * - y: 聊天扩展事件 (ChatExtensionEvent)
 * - E: 主面板提供器 (MainPanelProvider)
 * - v: WebView应用变更事件 (ChangeWebviewAppEvent)
 * - C: 入职会话事件报告器 (OnboardingSessionEventReporter)
 * - d: 同步启用跟踪器 (SyncingEnabledTracker)
 * - A: 扩展更新事件 (ExtensionUpdateEvent)
 * - k: 客户端指标报告器 (ClientMetricsReporter)
 * - b: 资产管理器 (AssetManager)
 * - w: Git操作服务 (GitOperationsService)
 *
 * 主要功能模块：
 * - 状态栏管理: this._statusBar = new Wee
 * - 工作计时器: this.workTimer = new ZZ
 * - 特性标志管理器: this.featureFlagManager = new iH
 * - 完成接受报告器: this._completionAcceptanceReporter = new UZ
 * - 代码编辑报告器: this._codeEditReporter = new qZ
 * - 下一步编辑解决报告器: this._nextEditResolutionReporter = new jZ
 * - 下一步编辑会话事件报告器: this._nextEditSessionEventReporter = new KZ
 * - 下一步编辑配置管理器: this.nextEditConfigManager = new wK
 * - 客户端指标报告器: this._clientMetricsReporter = new QZ
 * - 完成时间线报告器: this._completionTimelineReporter = new VZ
 * - 扩展事件报告器: this._extensionEventReporter = new GZ
 * - 特征向量报告器: this._featureVectorReporter = new LZ
 * - 指导原则监听器: this.guidelinesWatcher = new Lg
 * - 规则监听器: this.rulesWatcher = new c4
 * - 工具使用请求事件报告器: this._toolUseRequestEventReporter = new $N
 * - 完成模型: this._completionsModel = new vZ
 */

// ============================================================================
// VSCode插件核心类和激活函数
// ============================================================================

/**
 * VSCode Augment插件主类 - U9
 * 这是整个插件的核心控制器，管理所有功能模块
 */
class AugmentExtension extends ze {  // U9 -> AugmentExtension, ze -> BaseClass
    constructor(extensionContext, globalState, configListener, apiServer, auth,
                recentCompletions, recentInstructions, recentNextEditResults,
                recentChats, nextEditWebViewEvent, onExtensionUpdateEvent,
                mainPanelProvider, changeWebviewAppEvent, actionsModel,
                syncingEnabledTracker, chatExtensionEvent, onboardingSessionEventReporter,
                assetManager, gitOperationsService) {

        super();

        // 核心上下文和状态管理
        this._extensionContext = extensionContext;           // VSCode扩展上下文
        this._globalState = globalState;                     // 全局状态存储
        this._augmentConfigListener = configListener;       // 配置监听器
        this._apiServer = apiServer;                         // API服务器
        this._auth = auth;                                   // 认证管理器

        // 最近活动记录
        this._recentCompletions = recentCompletions;         // 最近的代码补全
        this._recentInstructions = recentInstructions;       // 最近的指令
        this._recentNextEditResults = recentNextEditResults; // 最近的下一步编辑结果
        this._recentChats = recentChats;                     // 最近的聊天记录

        // 事件发射器
        this._nextEditWebViewEvent = nextEditWebViewEvent;   // 下一步编辑WebView事件
        this._onExtensionUpdateEvent = onExtensionUpdateEvent; // 扩展更新事件
        this._changeWebviewAppEvent = changeWebviewAppEvent; // WebView应用变更事件
        this._chatExtensionEvent = chatExtensionEvent;       // 聊天扩展事件

        // UI和状态管理
        this._mainPanelProvider = mainPanelProvider;         // 主面板提供器
        this._actionsModel = actionsModel;                   // 操作模型
        this._syncingEnabledTracker = syncingEnabledTracker; // 同步启用跟踪器
        this._onboardingSessionEventReporter = onboardingSessionEventReporter; // 入职会话事件报告器
        this._assetManager = assetManager;                   // 资产管理器
        this._gitOperationsService = gitOperationsService;   // Git操作服务

        // 初始化核心组件
        this._statusBar = new StatusBarManager();           // Wee -> StatusBarManager
        extensionContext.subscriptions.push(this._statusBar);

        this.workTimer = new WorkTimer();                   // ZZ -> WorkTimer

        // 特性标志管理器
        this.featureFlagManager = new FeatureFlagManager({  // iH -> FeatureFlagManager
            fetcher: this._fetchFeatureFlags.bind(this),
            refreshIntervalMSec: 1800000  // 30分钟
        }, this._augmentConfigListener);

        // 各种事件报告器
        this._completionAcceptanceReporter = new CompletionAcceptanceReporter(apiServer, this._onboardingSessionEventReporter); // UZ -> CompletionAcceptanceReporter
        this._codeEditReporter = new CodeEditReporter(apiServer);                    // qZ -> CodeEditReporter
        this._nextEditResolutionReporter = new NextEditResolutionReporter(apiServer); // jZ -> NextEditResolutionReporter
        this._nextEditSessionEventReporter = new NextEditSessionEventReporter(apiServer); // KZ -> NextEditSessionEventReporter

        // 配置管理器
        this.nextEditConfigManager = new NextEditConfigManager(               // wK -> NextEditConfigManager
            this._augmentConfigListener,
            this.featureFlagManager,
            this._globalState
        );

        // 更多报告器
        this._clientMetricsReporter = new ClientMetricsReporter(apiServer);   // QZ -> ClientMetricsReporter
        this._completionTimelineReporter = new CompletionTimelineReporter(apiServer); // VZ -> CompletionTimelineReporter
        this._extensionEventReporter = new ExtensionEventReporter(apiServer); // GZ -> ExtensionEventReporter
        this._featureVectorReporter = new FeatureVectorReporter(apiServer, extensionContext); // LZ -> FeatureVectorReporter

        // 文件监听器
        this.guidelinesWatcher = new GuidelinesWatcher(                       // Lg -> GuidelinesWatcher
            this._augmentConfigListener,
            this.featureFlagManager,
            this._clientMetricsReporter
        );
        this.rulesWatcher = new RulesWatcher(this.workspaceManager);          // c4 -> RulesWatcher

        // 工具使用请求事件报告器
        this._toolUseRequestEventReporter = new ToolUseRequestEventReporter(); // $N -> ToolUseRequestEventReporter

        // 添加到销毁列表
        this.disposeOnDisable.push(this.guidelinesWatcher);
        this.disposeOnDisable.push(this.rulesWatcher);
        this.addDisposable(new Bt.Disposable(() => this.disable()));

        // 完成模型
        this._completionsModel = new CompletionsModel(                        // vZ -> CompletionsModel
            this,
            this._augmentConfigListener,
            this._clientMetricsReporter
        );

        // macOS证书处理
        if (!isVSCodeVersionAtLeast("1.96.0")) {  // qo -> isVSCodeVersionAtLeast
            try {
                this._logger.info("Starting macCA");
                addToGlobalAgent();  // pkt.addToGlobalAgent -> addToGlobalAgent
                this._logger.info("macCa Done");
            } catch (error) {
                this._logger.error("Exception loading mac-ca certs:", error);
            }
        }
    }

    // 静态属性
    static augmentRootName = ".augmentroot";
    static contentScheme = "augment";
    static displayStatusUri = Bt.Uri.from({
        scheme: this.contentScheme,
        path: "Augment Extension Status"
    });
    static modelConfigBackoffMsecMax = 30000; // 30秒

    // 实例属性
    keybindingWatcher = undefined;
    _completionServer = undefined;
    workspaceManager = undefined;

    // 更多核心属性
    _modelInfo = undefined;                          // 模型信息
    _blobNameCalculator = undefined;                 // Blob名称计算器
    _suggestionManager = undefined;                  // 建议管理器
    _nextEditRequestManager = undefined;             // 下一步编辑请求管理器
    _editorNextEdit = undefined;                     // 编辑器下一步编辑
    _backgroundNextEdit = undefined;                 // 后台下一步编辑
    _globalNextEdit = undefined;                     // 全局下一步编辑
    _diagnosticsManager = undefined;                 // 诊断管理器
    _nextEditVSCodeToWebviewMessage = new Bt.EventEmitter(); // VSCode到WebView消息事件发射器
    _openChatHintManager = undefined;                // 打开聊天提示管理器
    _remoteWorkspaceResolver = undefined;            // 远程工作区解析器
    _analyticsManager = undefined;                   // 分析管理器
    _inlineCompletionProvider = undefined;           // 内联补全提供器
    _chatModel = undefined;                          // 聊天模型
    _currentChatExtensionEventDisposable = undefined; // 当前聊天扩展事件可销毁对象
    _notificationWatcher = undefined;                // 通知监听器
    _toolsModel = undefined;                         // 工具模型
    _taskManager = undefined;                        // 任务管理器
    _exchangeManager = undefined;                    // 交换管理器
    _toolUseStateManager = undefined;                // 工具使用状态管理器
    _agentCheckpointManager = undefined;             // 代理检查点管理器
    _toolConfigStore = undefined;                    // 工具配置存储
    toolApprovalConfigManager = undefined;           // 工具批准配置管理器
    syncingStatusReporter = undefined;               // 同步状态报告器
    fuzzyFsSearcher = undefined;                     // 模糊文件系统搜索器
    fuzzySymbolSearcher = undefined;                 // 模糊符号搜索器

    // 状态属性
    enabled = false;                                 // 是否已启用
    enableInProgress = false;                        // 是否正在启用中
    disposeOnDisable = [];                          // 禁用时需要销毁的对象列表
    _enableCancel = undefined;                       // 启用取消令牌
    _statusTrace = undefined;                        // 状态跟踪
    _dataCollector = undefined;                      // 数据收集器
    _logger = createLogger("AugmentExtension");      // Ie -> createLogger

    // 获取会话ID
    get sessionId() {
        return this._apiServer.sessionId;
    }

    // 获取是否准备就绪
    get ready() {
        return this.enabled && this._modelInfo !== undefined;
    }

    // 获取可用模型列表
    get _availableModels() {
        return this._modelInfo?.models?.map(model => model.name) || [];
    }

    // 获取默认模型
    get _defaultModel() {
        return this._modelInfo?.defaultModel;
    }

    // 获取支持的语言列表
    get _languages() {
        return this._modelInfo?.languages || [];
    }
}

// ============================================================================
// VSCode插件激活函数
// ============================================================================

/**
 * VSCode插件激活函数 - qun
 * 这是VSCode插件的主入口点，当插件被激活时调用
 *
 * @param {vscode.ExtensionContext} extensionContext - VSCode扩展上下文
 */
function activateAugmentExtension(extensionContext) {  // qun -> activateAugmentExtension
    let logger = createLogger("activate()");  // Ie -> createLogger
    logger.debug("======== Activating extension ========");

    let augmentExtension; // s -> augmentExtension

    // 启用扩展
    function enableExtension(extension) {  // e -> enableExtension
        extension.enable();
    }

    // 禁用扩展
    function disableExtension(extension) {  // t -> disableExtension
        extension.disable();
    }

    // 重新加载扩展
    function reloadExtension() {  // i -> reloadExtension
        logger.info("======== Reloading extension ========");
        disableExtension(augmentExtension);
        enableExtension(augmentExtension);
    }

    // 注册扩展销毁处理器
    extensionContext.subscriptions.push(new Bt.Disposable(() => {
        if (augmentExtension) {
            logger.debug("======== Deactivating extension ========");
            disableExtension(augmentExtension);
        }
        augmentExtension = undefined;
    }));

    // 注册URI处理器
    extensionContext.subscriptions.push(Bt.window.registerUriHandler({
        handleUri(uri) {
            // 检查URI权限
            if (uri.authority.toLowerCase() !== extensionContext.extension.id.toLowerCase()) {
                logger.warn("Ignoring URI " + uri.toString());
                return;
            }

            // 处理MCP OAuth回调
            if (uri.path.startsWith("/auth/mcp")) {
                logger.info("Detected MCP OAuth callback, routing to handler");
                handleMCPOAuthCallback(uri, augmentExtension?.toolsModel, extensionContext, augmentExtension?.toolConfigStore); // qAt -> handleMCPOAuthCallback
                return;
            }

            // 处理其他URI路径
            switch (uri.path) {
                case AuthRedirectURIHandler.authRedirectURI.path:  // S.authRedirectURI.path
                    AuthRedirectURIHandler.handleAuthURI(uri);     // S.handleAuthURI
                    break;

                case URIActions.openChat:  // zte.openChat
                    var mode = new URLSearchParams(uri.query).get("mode");
                    if (mode && !["agent", "chat"].includes(mode)) {
                        logger.error("Invalid chat mode: " + mode);
                    } else {
                        Bt.commands.executeCommand(OpenChatCommand.commandID, mode); // zC.commandID
                    }
                    break;

                case URIActions.openAugmentSettings:  // zte.openAugmentSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID);     // H9.commandID
                    break;

                case URIActions.openGuidelinesSettings:  // zte.openGuidelinesSettings
                    Bt.commands.executeCommand(OpenSettingsCommand.commandID, "userGuidelines"); // H9.commandID
                    break;

                case URIActions.openMemories:  // zte.openMemories
                    var memoriesPath = augmentExtension?.toolsModel?.memoriesAbsPath;
                    if (memoriesPath) {
                        var memoriesUri = Bt.Uri.file(memoriesPath);
                        Bt.commands.executeCommand("vscode.open", memoriesUri);
                    } else {
                        logger.warn("Could not open memories: path not found.");
                    }
                    break;

                default:
                    logger.error("Unhandled URI " + Bt.Uri.from({
                        scheme: uri.scheme,
                        authority: uri.authority,
                        path: uri.path
                    }).toString());
            }
        }
    }));

    // 构建用户代理字符串
    var platformInfo = `${os.platform()}; ${os.arch()}; ${os.release()}`;  // Mte.default -> os
    var userAgent = `${extensionContext.extension.id}/${extensionContext.extension.packageJSON.version} (${platformInfo}) ${Bt.env.uriScheme}/${Bt.version}`;

    // 创建核心组件
    var globalState = new GlobalStateManager(extensionContext);  // JV -> GlobalStateManager
    extensionContext.subscriptions.push(globalState);

    let sessionId = generateSessionId(globalState);  // j8e -> generateSessionId
    let configListener = new ConfigurationListener(); // fH -> ConfigurationListener
    configListener.migrateLegacyConfig();

    var authManager = new AuthenticationManager(extensionContext, configListener); // yH -> AuthenticationManager
    extensionContext.subscriptions.push(authManager);

    var statusReporter = new StatusReporter(); // Vee -> StatusReporter
    extensionContext.subscriptions.push(statusReporter);

    // 初始化配置管理器
    initializeConfigManager(new ConfigManager(configListener)); // qTe, mK -> initializeConfigManager, ConfigManager
    initializeGlobalConfigManager(new GlobalConfigManager(authManager, configListener)); // $Te, gK -> initializeGlobalConfigManager, GlobalConfigManager

    // 创建API服务器
    let apiServer = new APIServer(configListener, authManager, sessionId, userAgent, global.fetch); // dH -> APIServer
    let statusBar = new StatusBarManager(); // kZ -> StatusBarManager

    // 创建最近活动记录器
    let recentCompletions = new RecentActivityTracker(10);    // rA -> RecentActivityTracker
    let recentInstructions = new RecentActivityTracker(10);
    let recentNextEditResults = new RecentActivityTracker(10);

    // 创建事件发射器
    let nextEditWebViewEvent = new Bt.EventEmitter();
    let onExtensionUpdateEvent = new Bt.EventEmitter();
    let changeWebviewAppEvent = new Bt.EventEmitter();
    let chatExtensionEvent = new Bt.EventEmitter();

    // 创建主面板提供器
    let mainPanelProvider = new MainPanelProvider(extensionContext); // fK -> MainPanelProvider

    // 创建任务管理器
    let taskManager = new TaskManager(new TaskManagerConfig({  // FN, m8 -> TaskManager, TaskManagerConfig
        defaultBackupDir: extensionContext.storageUri?.fsPath
    }));

    // 创建资产管理器
    let assetManager = new AssetManager(extensionContext.extensionUri); // Nee -> AssetManager
    assetManager.onVisibilityChange(isVisible => {
        if (!isVisible) {
            ChatPanel.currentPanel?.dispose(); // mC -> ChatPanel
        }
    });

    // 创建操作模型
    var actionsModel = new ActionsModel(globalState); // BZ -> ActionsModel
    extensionContext.subscriptions.push(actionsModel);

    // 创建同步启用跟踪器
    var syncingEnabledTracker = new SyncingEnabledTracker(apiServer); // JZ -> SyncingEnabledTracker

    // 创建认证状态管理器
    var authStateManager = new AuthenticationStateManager(apiServer, configListener, syncingEnabledTracker); // tK -> AuthenticationStateManager

    // 创建入职会话事件报告器
    var onboardingSessionEventReporter = new OnboardingSessionEventReporter(apiServer, configListener, authStateManager, actionsModel); // FZ -> OnboardingSessionEventReporter

    // 状态处理函数
    function handleSystemStates(states) {  // O -> handleSystemStates
        stateLoop: for (var state of states) {
            switch (state.name) {
                case "UserShouldSignIn":
                    assetManager.changeApp(onboardingSessionEventReporter);
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
                case "WorkspaceNotSelected":
                    if (actionsModel.isSystemStateComplete("authenticated")) {
                        changeWebviewAppEvent.fire("folder-selection");
                    }
                    break stateLoop;
                case "ShouldDisableCopilot":
                case "ShouldDisableCodeium":
                case "SyncingPermissionNeeded":
                case "uploadingHomeDir":
                case "workspaceTooLarge":
                    showPanelIfNeeded(configListener.config);
                    break stateLoop;
            }
        }

        // 检查是否需要同步权限
        if (actionsModel.isDerivedStateSatisfied("SyncingPermissionNeeded") ||
            actionsModel.isDerivedStateSatisfied("uploadingHomeDir") ||
            actionsModel.isDerivedStateSatisfied("workspaceTooLarge")) {
            changeWebviewAppEvent.fire("awaiting-syncing-permission");
        }
    }

    // 显示面板函数
    function showPanelIfNeeded(config) {  // W -> showPanelIfNeeded
        if (!config.disableFocusOnAugmentPanel && !assetManager.isVisible()) {
            Bt.commands.executeCommand(ShowPanelCommand.commandID); // uo.commandID
        }
    }

    // 注册会话变更监听器
    extensionContext.subscriptions.push(authManager.onDidChangeSession(() => {
        reloadExtension();
    }));

    // 注册状态监听器
    extensionContext.subscriptions.push(new StateListener(actionsModel, authManager, configListener)); // bH -> StateListener

    // 注册WebView提供器
    extensionContext.subscriptions.push(Bt.window.registerWebviewViewProvider("augment-chat", assetManager, {
        webviewOptions: {
            retainContextWhenHidden: true
        }
    }));

    // 创建主扩展实例
    augmentExtension = new AugmentExtension(  // U9 -> AugmentExtension
        extensionContext,
        globalState,
        configListener,
        apiServer,
        authManager,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        recentInstructions,
        nextEditWebViewEvent,
        chatExtensionEvent,
        assetManager,
        changeWebviewAppEvent,
        actionsModel,
        statusReporter,
        onExtensionUpdateEvent,
        syncingEnabledTracker,
        mainPanelProvider,
        taskManager
    );

    // 注册状态监听器
    extensionContext.subscriptions.push(actionsModel.onDerivedStatesSatisfied(handleSystemStates));
    handleSystemStates(actionsModel.satisfiedStates);

    // 监听配置变更
    configListener.onDidChange(configChange => {
        // 检查调试功能是否变更
        configChange.newConfig.enableDebugFeatures;
        configChange.previousConfig.enableDebugFeatures;
    });

    // 监听重要配置变更并重新加载扩展
    extensionContext.subscriptions.push(configListener.onDidChange(configChange => {
        let shouldReload = false;

        // 检查关键配置项是否变更
        for (var configKey of ["apiToken", "completionURL", "oauth", "modelName"]) {
            if (!isEqual(configChange.previousConfig[configKey], configChange.newConfig[configKey])) { // G8e.default -> isEqual
                shouldReload = true;
                break;
            }
        }

        if (shouldReload) {
            logger.info("Reloading extension due to config change");
            reloadExtension();
        }
    }));

    // 注册内容提供器
    extensionContext.subscriptions.push(Bt.workspace.registerTextDocumentContentProvider(AugmentExtension.contentScheme, augmentExtension));

    // 初始化其他功能
    initializeDebugFeatures(extensionContext); // Dft -> initializeDebugFeatures
    setupContextKeySync(augmentExtension, configListener, extensionContext); // fkt -> setupContextKeySync

    // 注册命令和功能
    var commandRegistrations = registerCommands(  // kCt -> registerCommands
        extensionContext,
        augmentExtension,
        configListener,
        authManager,
        authStateManager,
        apiServer,
        statusBar,
        recentCompletions,
        recentNextEditResults,
        changeWebviewAppEvent,
        onExtensionUpdateEvent,
        statusReporter,
        globalState,
        extensionContext.workspaceState
    );
    extensionContext.subscriptions.push(commandRegistrations);

    // 注册事件发射器
    extensionContext.subscriptions.push(chatExtensionEvent);

    // 注册其他功能
    extensionContext.subscriptions.push(AnalyticsHandler.register(extensionContext, configListener)); // AH -> AnalyticsHandler
    extensionContext.subscriptions.push(ModelInfoHandler.register(extensionContext, configListener, augmentExtension)); // mH -> ModelInfoHandler

    // 启用扩展
    enableExtension(augmentExtension);
}

// ============================================================================
// 上下文键同步功能
// ============================================================================

/**
 * 设置VSCode上下文键
 * @param {Object} contextKeys - 上下文键对象
 */
function setContextKeys(contextKeys) {  // ckt -> setContextKeys
    for (var [key, value] of Object.entries(contextKeys)) {
        Bt.commands.executeCommand("setContext", key, value);
    }
}

/**
 * 设置上下文键同步
 * @param {AugmentExtension} extension - 扩展实例
 * @param {ConfigurationListener} configListener - 配置监听器
 * @param {vscode.ExtensionContext} extensionContext - 扩展上下文
 */
function setupContextKeySync(extension, configListener, extensionContext) {  // fkt -> setupContextKeySync
    // 基础上下文键更新函数
    var updateBasicContextKeys = () => {  // a -> updateBasicContextKeys
        var config = configListener.config;
        setContextKeys({
            "vscode-augment.enableDebugFeatures": config.enableDebugFeatures,
            "vscode-augment.enableReviewerWorkflows": config.enableReviewerWorkflows,
            "vscode-augment.enableNextEdit": isNextEditEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // kf -> isNextEditEnabled
            "vscode-augment.enableNextEditBackgroundSuggestions": isNextEditBackgroundEnabled(configListener.config, extension?.featureFlagManager.currentFlags.vscodeNextEditMinVersion ?? ""), // vO -> isNextEditBackgroundEnabled
            "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeGenerateCommitMessageMinVersion ?? ""), // or -> isVersionAtLeast
            "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel,
            "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(extension?.featureFlagManager.currentFlags.vscodeBackgroundAgentsMinVersion ?? "") ?? false
        });
    };

    // 初始化基础上下文键
    updateBasicContextKeys();
    extensionContext.subscriptions.push(configListener.onDidChange(updateBasicContextKeys));

    // 特性标志相关的上下文键
    var featureFlagKeys = [  // s -> featureFlagKeys
        "enableWorkspaceManagerUi",
        "enableSmartPaste",
        "enableSmartPasteMinVersion",
        "enableInstructions",
        "vscodeSourcesMinVersion",
        "vscodeChatHintDecorationMinVersion",
        "vscodeEnableCpuProfile",
        "vscodeNextEditMinVersion",
        "vscodeGenerateCommitMessageMinVersion"
    ];

    // 特性标志上下文键更新函数
    var updateFeatureFlagContextKeys = () => {  // i -> updateFeatureFlagContextKeys
        if (extension) {
            var flags = extension.featureFlagManager.currentFlags;
            setContextKeys({
                "vscode-augment.workspace-manager-ui.enabled": flags.enableWorkspaceManagerUi,
                "vscode-augment.internal-new-instructions.enabled": flags.enableInstructions,
                "vscode-augment.internal-dv.enabled": isVersionAtLeast(flags.enableSmartPasteMinVersion) || flags.enableInstructions,
                "vscode-augment.sources-enabled": isVersionAtLeast(flags.vscodeSourcesMinVersion) ?? false,
                "vscode-augment.chat-hint.decoration": isVersionAtLeast(flags.vscodeChatHintDecorationMinVersion) ?? false,
                "vscode-augment.cpu-profile.enabled": flags.vscodeEnableCpuProfile,
                "vscode-augment.enableGenerateCommitMessage": isVersionAtLeast(flags.vscodeGenerateCommitMessageMinVersion) ?? false,
                "vscode-augment.featureFlags.enableRemoteAgents": isVersionAtLeast(flags.vscodeBackgroundAgentsMinVersion) ?? false,
                "vscode-augment.nextEdit.enablePanel": extension.nextEditConfigManager.config.enablePanel
            });
        }
    };

    // 初始化特性标志上下文键
    updateFeatureFlagContextKeys();
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateFeatureFlagContextKeys));
    extensionContext.subscriptions.push(extension.featureFlagManager.subscribe(featureFlagKeys, updateBasicContextKeys));
}

// 导出上下文键同步功能
var ContextKeySync = {  // Uun -> ContextKeySync
    setupContextKeySync: setupContextKeySync
};

// ============================================================================
// 异步工具函数模块
// ============================================================================

// Promise超时和回调处理工具
var promiseUtils = moduleWrapper((exports, module) => {  // mne -> promiseUtils
    // Promise超时包装器
    function promiseTimeout(promise, timeoutMs) {
        return new Promise(function(resolve, reject) {
            var timeoutId = setTimeout(function() {
                reject(Error("Promise timed out"))
            }, timeoutMs);
            promise.then(function(result) {
                return clearTimeout(timeoutId), resolve(result)
            }).catch(reject)
        })
    }

    // 延迟执行工具
    function sleep(delayMs) {
        return new Promise(function(resolve) {
            return setTimeout(resolve, delayMs)
        })
    }

    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.invokeCallback = exports.sleep = exports.pTimeout = void 0;
    exports.pTimeout = promiseTimeout;
    exports.sleep = sleep;

    // 回调函数调用器 - 带超时和错误处理
    exports.invokeCallback = function(context, callback, delay) {
        return sleep(delay).then(function() {
            return promiseTimeout((() => {
                try {
                    return Promise.resolve(callback(context))
                } catch (error) {
                    return Promise.reject(error)
                }
            })(), 1000)  // 1秒超时
        }).catch(function(error) {
            context?.log("warn", "Callback Error", {
                error: error
            });
            context?.stats.increment("callback_error")
        }).then(function() {
            return context
        })
    }
});

// ============================================================================
// Deferred Promise 工具
// ============================================================================

// 可延迟解决的Promise创建器
var createDeferredPromise = moduleWrapper((exports, module) => {  // CSe -> createDeferredPromise
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createDeferred = void 0;

    exports.createDeferred = function() {
        var resolveFunc, rejectFunc, isSettled = false,
            promise = new Promise(function(resolve, reject) {
                resolveFunc = function() {
                    for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
                    isSettled = true;
                    resolve.apply(void 0, args)
                };
                rejectFunc = function() {
                    for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
                    isSettled = true;
                    reject.apply(void 0, args)
                }
            });
        return {
            resolve: resolveFunc,
            reject: rejectFunc,
            promise: promise,
            isSettled: function() {
                return isSettled
            }
        }
    }
});

// Deferred Promise 导出模块
var deferredExports = moduleWrapper((exports, module) => {  // SSe -> deferredExports
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    markAsESModule(commonJSUtils).__exportStar(createDeferredPromise(), exports)
});

// ============================================================================
// 事件发射器 (Event Emitter) 模块
// ============================================================================

// 事件发射器实现
var eventEmitter = moduleWrapper((exports, module) => {  // xSe -> eventEmitter
    function EventEmitter(options) {
        this.callbacks = {};
        this.warned = false;
        this.maxListeners = null != (options = options?.maxListeners) ? options : 10
    }

    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.Emitter = void 0;

    // 内存泄漏警告检查
    EventEmitter.prototype.warnIfPossibleMemoryLeak = function(eventName) {
        if (!this.warned && this.maxListeners && this.callbacks[eventName].length > this.maxListeners) {
            console.warn("Event Emitter: Possible memory leak detected; ".concat(String(eventName), " has exceeded ").concat(this.maxListeners, " listeners."));
            this.warned = true
        }
    };

    // 添加事件监听器
    EventEmitter.prototype.on = function(eventName, callback) {
        if (this.callbacks[eventName]) {
            this.callbacks[eventName].push(callback);
            this.warnIfPossibleMemoryLeak(eventName)
        } else {
            this.callbacks[eventName] = [callback]
        }
        return this
    };

    // 添加一次性事件监听器
    EventEmitter.prototype.once = function(eventName, callback) {
        function onceWrapper() {
            for (var args = [], i = 0; i < arguments.length; i++) args[i] = arguments[i];
            emitterInstance.off(eventName, onceWrapper);
            callback.apply(emitterInstance, args)
        }
        var emitterInstance = this;
        return this.on(eventName, onceWrapper), this
    };

    // 移除事件监听器
    EventEmitter.prototype.off = function(eventName, callback) {
        var filteredCallbacks = (null != (filteredCallbacks = this.callbacks[eventName]) ? filteredCallbacks : []).filter(function(cb) {
            return cb !== callback
        });
        return this.callbacks[eventName] = filteredCallbacks, this
    };

    // 触发事件
    EventEmitter.prototype.emit = function(eventName) {
        for (var emitterInstance = this, args = [], i = 1; i < arguments.length; i++) args[i - 1] = arguments[i];
        return (null != (eventName = this.callbacks[eventName]) ? eventName : []).forEach(function(callback) {
            callback.apply(emitterInstance, args)
        }), this
    };

    exports.Emitter = EventEmitter
});

// 事件发射器导出模块
var emitterExports = moduleWrapper((exports, module) => {  // kSe -> emitterExports
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    markAsESModule(commonJSUtils).__exportStar(eventEmitter(), exports)
});

// 工具模块聚合导出
var toolsAggregator = moduleWrapper((exports, module) => {  // TA -> toolsAggregator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);
    commonJSUtils.__exportStar(deferredExports(), exports);
    commonJSUtils.__exportStar(emitterExports(), exports)
});

// ============================================================================
// 退避算法模块 (Backoff Algorithm)
// ============================================================================

// 指数退避算法实现
var backoffAlgorithm = moduleWrapper((exports, module) => {  // bne -> backoffAlgorithm
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.backoff = void 0;

    // 计算退避延迟时间
    exports.backoff = function(options) {
        var randomFactor = Math.random() + 1,
            minTimeout = options.minTimeout,
            factor = options.factor,
            maxTimeout = void 0 === (maxTimeout = options.maxTimeout) ? Infinity : maxTimeout;
        return Math.min(randomFactor * (void 0 === minTimeout ? 500 : minTimeout) * Math.pow(void 0 === factor ? 2 : factor, options.attempt), maxTimeout)
    }
});

// ============================================================================
// 优先级队列模块 (Priority Queue)
// ============================================================================

// 优先级队列实现 - 用于任务调度和重试机制
var priorityQueue = moduleWrapper((exports, module) => {  // vne -> priorityQueue
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.PriorityQueue = exports.ON_REMOVE_FROM_FUTURE = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        toolsModule = toolsAggregator(),
        backoffModule = backoffAlgorithm();

    exports.ON_REMOVE_FROM_FUTURE = "onRemoveFromFuture";

    var EmitterBase = toolsModule.Emitter;

    // 优先级队列类 - 继承自事件发射器
    function PriorityQueue(maxAttempts, initialQueue, seenItems) {
        var instance = EmitterBase.call(this) || this;
        instance.future = [];  // 未来执行的任务队列
        instance.maxAttempts = maxAttempts;  // 最大重试次数
        instance.queue = initialQueue;  // 当前队列
        instance.seen = seenItems ?? {};  // 已见过的项目记录
        return instance
    }

    commonJSUtils.__extends(PriorityQueue, EmitterBase);

    // 向队列添加项目
    PriorityQueue.prototype.push = function() {
        for (var queueInstance = this, items = [], i = 0; i < arguments.length; i++) items[i] = arguments[i];

        var results = items.map(function(item) {
            return !(queueInstance.updateAttempts(item) > queueInstance.maxAttempts ||
                    queueInstance.includes(item) ||
                    (queueInstance.queue.push(item), 0))
        });

        // 按尝试次数排序队列
        this.queue = this.queue.sort(function(a, b) {
            return queueInstance.getAttempts(a) - queueInstance.getAttempts(b)
        });

        return results
    };

    // 带退避延迟的队列添加
    PriorityQueue.prototype.pushWithBackoff = function(item, minDelay) {
        var attempts, queueInstance = this;
        minDelay = void 0 === minDelay ? 0 : minDelay;

        // 如果是首次尝试且没有最小延迟，直接添加
        if (0 == minDelay && 0 === this.getAttempts(item)) {
            return this.push(item)[0]
        }

        attempts = this.updateAttempts(item);

        // 检查是否超过最大尝试次数或已包含在队列中
        if (attempts > this.maxAttempts || this.includes(item)) {
            return false
        }

        // 计算退避延迟
        var backoffDelay = backoffModule.backoff({
            attempt: attempts - 1
        });

        // 应用最小延迟
        if (0 < minDelay && backoffDelay < minDelay) {
            backoffDelay = minDelay
        }

        // 延迟添加到队列
        setTimeout(function() {
            queueInstance.queue.push(item);
            queueInstance.future = queueInstance.future.filter(function(futureItem) {
                return futureItem.id !== item.id
            });
            queueInstance.emit(exports.ON_REMOVE_FROM_FUTURE)
        }, backoffDelay);

        this.future.push(item);
        return true
    };

    // 获取项目的尝试次数
    PriorityQueue.prototype.getAttempts = function(item) {
        return null != (item = this.seen[item.id]) ? item : 0
    };

    // 更新项目的尝试次数
    PriorityQueue.prototype.updateAttempts = function(item) {
        this.seen[item.id] = this.getAttempts(item) + 1;
        return this.getAttempts(item)
    };

    // 检查队列是否包含指定项目
    PriorityQueue.prototype.includes = function(targetItem) {
        return this.queue.includes(targetItem) ||
               this.future.includes(targetItem) ||
               !!this.queue.find(function(item) {
                   return item.id === targetItem.id
               }) ||
               !!this.future.find(function(item) {
                   return item.id === targetItem.id
               })
    };

    // 从队列中取出下一个项目
    PriorityQueue.prototype.pop = function() {
        return this.queue.shift()
    };

    // 队列长度属性
    Object.defineProperty(PriorityQueue.prototype, "length", {
        get: function() {
            return this.queue.length
        },
        enumerable: false,
        configurable: true
    });

    // 总待处理项目数属性
    Object.defineProperty(PriorityQueue.prototype, "todo", {
        get: function() {
            return this.queue.length + this.future.length
        },
        enumerable: false,
        configurable: true
    });

    exports.PriorityQueue = PriorityQueue
});

// ============================================================================
// UUID生成器模块
// ============================================================================

// 简化的UUID v4生成器
var uuidGenerator = moduleWrapper((exports, module) => {  // yne -> uuidGenerator
    var hexChars, poolSize = 256, hexPool = [];

    // 预生成十六进制字符池
    for (; poolSize--;) {
        hexPool[poolSize] = (poolSize + 256).toString(16).substring(1)
    }

    // UUID v4生成函数
    exports.v4 = function() {
        var hexChar, index = 0, uuid = "";

        // 重新生成随机池（如果需要）
        if (!hexChars || 256 < poolSize + 16) {
            hexChars = Array(index = 256);
            for (; index--;) {
                hexChars[index] = 256 * Math.random() | 0
            }
            index = poolSize = 0
        }

        // 构建UUID字符串
        for (; index < 16; index++) {
            hexChar = hexChars[poolSize + index];
            uuid += 6 == index ? hexPool[15 & hexChar | 64] :  // 版本位
                    8 == index ? hexPool[63 & hexChar | 128] :  // 变体位
                    hexPool[hexChar];

            // 添加连字符
            if (1 & index && 1 < index && index < 11) {
                uuid += "-"
            }
        }
        poolSize++;
        return uuid
    }
});

// ============================================================================
// 核心日志记录器模块
// ============================================================================

// 核心日志记录器实现
var coreLogger = moduleWrapper((exports, module) => {  // Ene -> coreLogger
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreLogger = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    function CoreLogger() {
        this._logs = []  // 内部日志存储
    }

    // 记录日志条目
    CoreLogger.prototype.log = function(level, message, extras) {
        var timestamp = new Date;
        this._logs.push({
            level: level,
            message: message,
            time: timestamp,
            extras: extras
        })
    };

    // 日志访问器属性
    Object.defineProperty(CoreLogger.prototype, "logs", {
        get: function() {
            return this._logs
        },
        enumerable: false,
        configurable: true
    });

    // 刷新日志到控制台
    CoreLogger.prototype.flush = function() {
        var formattedLogs;

        if (1 < this.logs.length) {
            // 多条日志时使用表格格式
            formattedLogs = this._logs.reduce(function(accumulator, logEntry) {
                var formattedEntry = commonJSUtils.__assign(commonJSUtils.__assign({}, logEntry), {
                        json: JSON.stringify(logEntry.extras, null, " "),
                        extras: logEntry.extras
                    }),
                    timeKey = (delete formattedEntry.time,
                              null != (timeKey = null == (timeKey = logEntry.time) ? void 0 : timeKey.toISOString()) ? timeKey : "");

                // 处理时间键冲突
                if (accumulator[timeKey]) {
                    timeKey = "".concat(timeKey, "-").concat(Math.random())
                }

                return commonJSUtils.__assign(commonJSUtils.__assign({}, accumulator),
                    ((accumulator = {})[timeKey] = formattedEntry, accumulator))
            }, {});

            // 输出表格或普通日志
            console.table ? console.table(formattedLogs) : console.log(formattedLogs)
        } else {
            // 单条日志直接输出
            this.logs.forEach(function(logEntry) {
                var level = logEntry.level,
                    message = logEntry.message,
                    extras = logEntry.extras;

                if ("info" === level || "debug" === level) {
                    console.log(message, extras ?? "")
                } else {
                    console[level](message, extras ?? "")
                }
            })
        }

        this._logs = []  // 清空日志
    };

    exports.CoreLogger = CoreLogger
});

// ============================================================================
// 统计指标模块
// ============================================================================

// 核心统计指标收集器
var coreStats = moduleWrapper((exports, module) => {  // Cne -> coreStats
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.NullStats = exports.CoreStats = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 核心统计类
    function CoreStats() {
        this.metrics = []  // 指标存储
    }

    // 增量计数器
    CoreStats.prototype.increment = function(metricName, value, tags) {
        this.metrics.push({
            metric: metricName,
            value: value = void 0 === value ? 1 : value,
            tags: tags ?? [],
            type: "counter",
            timestamp: Date.now()
        })
    };

    // 仪表盘指标
    CoreStats.prototype.gauge = function(metricName, value, tags) {
        this.metrics.push({
            metric: metricName,
            value: value,
            tags: tags ?? [],
            type: "gauge",
            timestamp: Date.now()
        })
    };

    // 刷新指标到控制台
    CoreStats.prototype.flush = function() {
        var formattedMetrics = this.metrics.map(function(metric) {
            return commonJSUtils.__assign(commonJSUtils.__assign({}, metric), {
                tags: metric.tags.join(",")
            })
        });
        console.table ? console.table(formattedMetrics) : console.log(formattedMetrics);
        this.metrics = []
    };

    // 序列化指标数据
    CoreStats.prototype.serialize = function() {
        return this.metrics.map(function(metric) {
            return {
                m: metric.metric,      // metric name
                v: metric.value,       // value
                t: metric.tags,        // tags
                k: {                   // kind/type
                    gauge: "g",
                    counter: "c"
                }[metric.type],
                e: metric.timestamp    // epoch timestamp
            }
        })
    };

    exports.CoreStats = CoreStats;

    // 空统计类 - 用于禁用统计收集的场景
    var StatsBase = CoreStats;
    function NullStats() {
        return null !== StatsBase && StatsBase.apply(this, arguments) || this
    }

    commonJSUtils.__extends(NullStats, StatsBase);

    // 空实现 - 所有方法都不执行任何操作
    NullStats.prototype.gauge = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.increment = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.flush = function() {
        for (var i = 0; i < arguments.length; i++) i, 0
    };

    NullStats.prototype.serialize = function() {
        for (var i = 0; i < arguments.length; i++) i, 0;
        return []
    };

    exports.NullStats = NullStats
});

// ============================================================================
// 核心上下文模块 (Core Context)
// ============================================================================

// 上下文取消和核心上下文实现
var coreContext = moduleWrapper((exports, module) => {  // Sz -> coreContext
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreContext = exports.ContextCancelation = void 0;

    var uuidModule = uuidGenerator(),
        dsetModule = dne(),  // 对象属性设置工具
        loggerModule = coreLogger(),
        statsModule = coreStats();

    // 上下文取消异常类
    function ContextCancelation(options) {
        var defaultValue;
        this.retry = null == (defaultValue = options.retry) || defaultValue;
        this.type = null != (defaultValue = options.type) ? defaultValue : "plugin Error";
        this.reason = null != (defaultValue = options.reason) ? defaultValue : ""
    }

    exports.ContextCancelation = ContextCancelation;

    // 核心上下文类 - 用于跟踪事件处理过程
    function CoreContext(event, contextId, stats, logger) {
        void 0 === contextId && (contextId = uuidModule.v4());
        void 0 === stats && (stats = new statsModule.NullStats);
        void 0 === logger && (logger = new loggerModule.CoreLogger);

        this.attempts = 0;          // 尝试次数
        this.event = event;         // 关联的事件
        this._id = contextId;       // 上下文ID
        this.logger = logger;       // 日志记录器
        this.stats = stats;         // 统计收集器
    }

    // 系统级静态方法
    CoreContext.system = function() {};

    // 检查是否为同一个上下文
    CoreContext.prototype.isSame = function(otherContext) {
        return otherContext.id === this.id
    };

    // 取消上下文执行
    CoreContext.prototype.cancel = function(error) {
        throw error || new ContextCancelation({
            reason: "Context Cancel"
        })
    };

    // 记录日志
    CoreContext.prototype.log = function(level, message, extras) {
        this.logger.log(level, message, extras)
    };

    // 上下文ID访问器
    Object.defineProperty(CoreContext.prototype, "id", {
        get: function() {
            return this._id
        },
        enumerable: false,
        configurable: true
    });

    // 更新事件属性
    CoreContext.prototype.updateEvent = function(propertyPath, value) {
        var integrations;

        // 特殊处理集成配置
        if ("integrations" === propertyPath.split(".")[0]) {
            var integrationName = propertyPath.split(".")[1];
            if (false === (null == (integrations = this.event.integrations) ? void 0 : integrations[integrationName])) {
                return this.event
            }
        }

        // 使用dset设置深层属性
        dsetModule.dset(this.event, propertyPath, value);
        return this.event
    };

    // 获取失败交付信息
    CoreContext.prototype.failedDelivery = function() {
        return this._failedDelivery
    };

    // 设置失败交付信息
    CoreContext.prototype.setFailedDelivery = function(failureInfo) {
        this._failedDelivery = failureInfo
    };

    // 获取日志记录
    CoreContext.prototype.logs = function() {
        return this.logger.logs
    };

    // 刷新日志和统计
    CoreContext.prototype.flush = function() {
        this.logger.flush();
        this.stats.flush()
    };

    // 序列化为JSON
    CoreContext.prototype.toJSON = function() {
        return {
            id: this._id,
            event: this.event,
            logs: this.logger.logs,
            metrics: this.stats.metrics
        }
    };

    exports.CoreContext = CoreContext
});

// ============================================================================
// 数组分组工具模块
// ============================================================================

// 数组分组工具
var arrayGroupBy = moduleWrapper((exports, module) => {  // OSe -> arrayGroupBy
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.groupBy = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 按指定键或函数对数组元素进行分组
    exports.groupBy = function(array, groupingKey) {
        var groups = {};

        array.forEach(function(item) {
            var keyValue, groupKey = void 0;

            if ("string" == typeof groupingKey) {
                // 按属性名分组
                keyValue = item[groupingKey];
                groupKey = "string" != typeof keyValue ? JSON.stringify(keyValue) : keyValue
            } else if (groupingKey instanceof Function) {
                // 按函数返回值分组
                groupKey = groupingKey(item)
            }

            if (void 0 !== groupKey) {
                groups[groupKey] = commonJSUtils.__spreadArray(
                    commonJSUtils.__spreadArray([], null != (keyValue = groups[groupKey]) ? keyValue : [], true),
                    [item],
                    false
                )
            }
        });

        return groups
    }
});

// ============================================================================
// Promise检测工具模块
// ============================================================================

// 检测对象是否为thenable (Promise-like)
var thenableDetector = moduleWrapper((exports, module) => {  // WSe -> thenableDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isThenable = void 0;

    exports.isThenable = function(obj) {
        return "object" == typeof obj && null !== obj && "then" in obj && "function" == typeof obj.then
    }
});

// ============================================================================
// 任务组管理模块
// ============================================================================

// 任务组创建器 - 用于管理异步任务的完成状态
var taskGroupCreator = moduleWrapper((exports, module) => {  // BSe -> taskGroupCreator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.createTaskGroup = void 0;

    var thenableModule = thenableDetector();

    exports.createTaskGroup = function() {
        var donePromise, resolveFunc, activeTaskCount = 0;

        return {
            // 获取所有任务完成的Promise
            done: function() {
                return donePromise
            },

            // 运行任务并跟踪其完成状态
            run: function(taskFunc) {
                var result = taskFunc();

                // 如果返回Promise，跟踪其完成状态
                if (thenableModule.isThenable(result)) {
                    if (1 == ++activeTaskCount) {
                        // 第一个任务时创建完成Promise
                        donePromise = new Promise(function(resolve) {
                            return resolveFunc = resolve
                        })
                    }

                    // 任务完成时减少计数
                    result.finally(function() {
                        return 0 == --activeTaskCount && resolveFunc()
                    })
                }

                return result
            }
        }
    }
});

// ============================================================================
// 插件执行工具模块
// ============================================================================

// 插件尝试执行和确保执行工具
var pluginExecutor = moduleWrapper((exports, module) => {  // xne -> pluginExecutor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.ensure = exports.attempt = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        contextModule = coreContext();

    // 尝试执行插件方法
    function attemptPluginExecution(context, plugin) {
        context.log("debug", "plugin", {
            plugin: plugin.name
        });

        var startTime = (new Date).getTime(),
            pluginMethod = plugin[context.event.type];

        // 如果插件没有对应事件类型的方法，直接返回
        if (void 0 === pluginMethod) {
            return Promise.resolve(context)
        }

        // 包装插件执行以处理异常
        function executeWithErrorHandling(executionFunc) {
            return commonJSUtils.__awaiter(this, void 0, void 0, function() {
                var error;
                return commonJSUtils.__generator(this, function(step) {
                    switch (step.label) {
                        case 0:
                            step.trys.push([0, 2, , 3]);
                            return [4, executionFunc()];
                        case 1:
                            return [2, step.sent()];
                        case 2:
                            error = step.sent();
                            return [2, Promise.reject(error)];
                        case 3:
                            return [2]
                    }
                })
            })
        }

        // 执行插件方法
        return executeWithErrorHandling(function() {
            return pluginMethod.apply(plugin, [context])
        }).then(function(result) {
            var executionTime = (new Date).getTime() - startTime;
            result.stats.gauge("plugin_time", executionTime, ["plugin:".concat(plugin.name)]);
            return result
        }).catch(function(error) {
            // 处理中间件取消异常
            if (error instanceof contextModule.ContextCancelation && "middleware_cancellation" === error.type) {
                throw error
            }

            // 记录错误
            if (error instanceof contextModule.ContextCancelation) {
                context.log("warn", error.type, {
                    plugin: plugin.name,
                    error: error
                })
            } else {
                context.log("error", "plugin Error", {
                    plugin: plugin.name,
                    error: error
                });
                context.stats.increment("plugin_error", 1, ["plugin:".concat(plugin.name)])
            }

            return error
        })
    }

    exports.attempt = attemptPluginExecution;

    // 确保插件执行成功，否则取消上下文
    exports.ensure = function(context, plugin) {
        return attemptPluginExecution(context, plugin).then(function(result) {
            if (result instanceof contextModule.CoreContext) {
                return result
            }

            // 如果不是有效的上下文，取消执行
            context.log("debug", "Context canceled");
            context.stats.increment("context_canceled");
            context.cancel(result)
        })
    }
});

// ============================================================================
// 核心事件队列模块 (Core Event Queue)
// ============================================================================

// 核心事件队列 - 负责管理和处理事件的分发、重试和插件执行
var coreEventQueue = moduleWrapper((exports, module) => {  // FSe -> coreEventQueue
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreEventQueue = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        groupByModule = arrayGroupBy(),
        priorityQueueModule = priorityQueue(),
        contextModule = coreContext(),
        toolsModule = toolsAggregator(),
        taskGroupModule = taskGroupCreator(),
        pluginExecutorModule = pluginExecutor();

    var EmitterBase = toolsModule.Emitter;

    // 核心事件队列类 - 继承自事件发射器
    function CoreEventQueue(queue) {
        var instance = EmitterBase.call(this) || this;
        instance.criticalTasks = taskGroupModule.createTaskGroup();  // 关键任务组
        instance.plugins = [];                    // 插件列表
        instance.failedInitializations = [];     // 失败的初始化记录
        instance.flushing = false;               // 是否正在刷新
        instance.queue = queue;                  // 事件队列

        // 监听队列的未来任务移除事件
        instance.queue.on(priorityQueueModule.ON_REMOVE_FROM_FUTURE, function() {
            instance.scheduleFlush(0)
        });

        return instance
    }

    commonJSUtils.__extends(CoreEventQueue, EmitterBase);

    // 注册插件
    CoreEventQueue.prototype.register = function(context, plugin, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var handleError, error, queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        this.plugins.push(plugin);

                        // 错误处理函数
                        handleError = function(err) {
                            queueInstance.failedInitializations.push(plugin.name);
                            queueInstance.emit("initialization_failure", plugin);
                            console.warn(plugin.name, err);
                            context.log("warn", "Failed to load destination", {
                                plugin: plugin.name,
                                error: err
                            });
                            queueInstance.plugins = queueInstance.plugins.filter(function(p) {
                                return p !== plugin
                            })
                        };

                        // 对于目标插件（非Segment.io），异步加载
                        if ("destination" === plugin.type && "Segment.io" !== plugin.name) {
                            plugin.load(context, options).catch(handleError);
                            return [3, 4]
                        }
                        return [3, 1];

                    case 1:
                        step.trys.push([1, 3, , 4]);
                        return [4, plugin.load(context, options)];

                    case 2:
                        step.sent();
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        handleError(error);
                        return [3, 4];

                    case 4:
                        return [2]
                }
            })
        })
    };

    // 注销插件
    CoreEventQueue.prototype.deregister = function(context, plugin, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var error;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        step.trys.push([0, 3, , 4]);

                        if (!plugin.unload) return [3, 2];
                        return [4, Promise.resolve(plugin.unload(context, options))];

                    case 1:
                        step.sent();

                    case 2:
                        this.plugins = this.plugins.filter(function(p) {
                            return p.name !== plugin.name
                        });
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        context.log("warn", "Failed to unload destination", {
                            plugin: plugin.name,
                            error: error
                        });
                        return [3, 4];

                    case 4:
                        return [2]
                }
            })
        })
    };

    // 分发事件到队列
    CoreEventQueue.prototype.dispatch = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var deliveryPromise;

            return commonJSUtils.__generator(this, function(step) {
                context.log("debug", "Dispatching");
                context.stats.increment("message_dispatched");
                this.queue.push(context);
                deliveryPromise = this.subscribeToDelivery(context);
                this.scheduleFlush(0);
                return [2, deliveryPromise]
            })
        })
    };

    // 订阅交付完成事件
    CoreEventQueue.prototype.subscribeToDelivery = function(targetContext) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                return [2, new Promise(function(resolve) {
                    function onFlush(context, success) {
                        if (context.isSame(targetContext)) {
                            queueInstance.off("flush", onFlush);
                            resolve(context)
                        }
                    }
                    queueInstance.on("flush", onFlush)
                })]
            })
        })
    };

    // 单独分发事件（不使用队列）
    CoreEventQueue.prototype.dispatchSingle = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var queueInstance = this;

            return commonJSUtils.__generator(this, function(step) {
                context.log("debug", "Dispatching");
                context.stats.increment("message_dispatched");
                this.queue.updateAttempts(context);
                context.attempts = 1;

                return [2, this.deliver(context).catch(function(error) {
                    if (queueInstance.enqueuRetry(error, context)) {
                        return queueInstance.subscribeToDelivery(context)
                    } else {
                        context.setFailedDelivery({
                            reason: error
                        });
                        return context
                    }
                })]
            })
        })
    };

    // 检查队列是否为空
    CoreEventQueue.prototype.isEmpty = function() {
        return 0 === this.queue.length
    };

    // 调度刷新操作
    CoreEventQueue.prototype.scheduleFlush = function(delay) {
        var queueInstance = this;
        void 0 === delay && (delay = 500);

        if (!this.flushing) {
            this.flushing = true;
            setTimeout(function() {
                queueInstance.flush().then(function() {
                    setTimeout(function() {
                        queueInstance.flushing = false;
                        if (queueInstance.queue.length) {
                            queueInstance.scheduleFlush(0)
                        }
                    }, 0)
                })
            }, delay)
        }
    };

    // 交付单个事件
    CoreEventQueue.prototype.deliver = function(context) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var startTime, deliveryTime, error, errorDetails;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        return [4, this.criticalTasks.done()];

                    case 1:
                        step.sent();
                        startTime = Date.now();

                    case 2:
                        step.trys.push([2, 4, , 5]);
                        return [4, this.flushOne(context)];

                    case 3:
                        context = step.sent();
                        deliveryTime = Date.now() - startTime;
                        this.emit("delivery_success", context);
                        context.stats.gauge("delivered", deliveryTime);
                        context.log("debug", "Delivered", context.event);
                        return [2, context];

                    case 4:
                        deliveryTime = step.sent();
                        errorDetails = deliveryTime;
                        context.log("error", "Failed to deliver", errorDetails);
                        this.emit("delivery_failure", context, errorDetails);
                        context.stats.increment("delivery_failed");
                        throw deliveryTime;

                    case 5:
                        return [2]
                }
            })
        })
    };

    // 将失败的事件加入重试队列
    CoreEventQueue.prototype.enqueuRetry = function(error, context) {
        // 检查是否应该重试
        var shouldRetry = !(error instanceof contextModule.ContextCancelation) || error.retry;
        return shouldRetry && this.queue.pushWithBackoff(context)
    };

    // 刷新队列中的下一个事件
    CoreEventQueue.prototype.flush = function() {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var context, error;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        if (0 === this.queue.length) return [2, []];

                        context = this.queue.pop();
                        if (!context) return [2, []];

                        context.attempts = this.queue.getAttempts(context);

                    case 1:
                        step.trys.push([1, 3, , 4]);
                        return [4, this.deliver(context)];

                    case 2:
                        context = step.sent();
                        this.emit("flush", context, true);
                        return [3, 4];

                    case 3:
                        error = step.sent();
                        if (!this.enqueuRetry(error, context)) {
                            context.setFailedDelivery({
                                reason: error
                            });
                            this.emit("flush", context, false)
                        }
                        return [2, []];

                    case 4:
                        return [2, [context]]
                }
            })
        })
    };

    // 检查队列是否准备就绪
    CoreEventQueue.prototype.isReady = function() {
        return true
    };

    // 获取可用的扩展插件（按类型分组）
    CoreEventQueue.prototype.availableExtensions = function(integrations) {
        var availablePlugins = this.plugins.filter(function(plugin) {
                var alternativeValue, alternativeNames;

                // 对于非目标插件或Segment.io，直接返回true
                if ("destination" !== plugin.type && "Segment.io" !== plugin.name) {
                    return true
                }

                // 检查插件的替代名称
                alternativeValue = void 0;
                if (null != (alternativeNames = plugin.alternativeNames)) {
                    alternativeNames.forEach(function(altName) {
                        if (void 0 !== integrations[altName]) {
                            alternativeValue = integrations[altName]
                        }
                    })
                }

                // 确定插件是否启用
                var isEnabled = null != (alternativeNames = null != (alternativeNames = integrations[plugin.name]) ? alternativeNames : alternativeValue) ? alternativeNames : false !== ("Segment.io" === plugin.name || integrations.All);
                return isEnabled
            }),
            groupedPlugins = groupByModule.groupBy(availablePlugins, "type"),
            beforePlugins = groupedPlugins.before,
            enrichmentPlugins = groupedPlugins.enrichment,
            destinationPlugins = groupedPlugins.destination,
            afterPlugins = groupedPlugins.after;

        return {
            before: void 0 === beforePlugins ? [] : beforePlugins,
            enrichment: void 0 === enrichmentPlugins ? [] : enrichmentPlugins,
            destinations: void 0 === destinationPlugins ? [] : destinationPlugins,
            after: void 0 === afterPlugins ? [] : afterPlugins
        }
    };

    // 处理单个事件的完整流程
    CoreEventQueue.prototype.flushOne = function(context) {
        var integrations;

        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var extensions, beforePlugins, enrichmentPlugins, i, plugin, result,
                updatedExtensions, destinationPlugins, afterPlugins, destinationPromises, afterPromises;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        if (!this.isReady()) {
                            throw new Error("Not ready")
                        }

                        // 如果是重试，发出重试事件
                        if (1 < context.attempts) {
                            this.emit("delivery_retry", context)
                        }

                        // 获取可用扩展
                        extensions = this.availableExtensions(null != (integrations = context.event.integrations) ? integrations : {});
                        beforePlugins = extensions.before;
                        enrichmentPlugins = extensions.enrichment;

                        // 执行before插件
                        i = 0;

                    case 1:
                        if (!(i < beforePlugins.length)) return [3, 4];

                        plugin = beforePlugins[i];
                        return [4, pluginExecutorModule.ensure(context, plugin)];

                    case 2:
                        result = step.sent();
                        if (result instanceof contextModule.CoreContext) {
                            context = result
                        }
                        this.emit("message_enriched", context, plugin);

                    case 3:
                        i++;
                        return [3, 1];

                    case 4:
                        // 执行enrichment插件
                        i = 0;

                    case 5:
                        if (!(i < enrichmentPlugins.length)) return [3, 8];

                        plugin = enrichmentPlugins[i];
                        return [4, pluginExecutorModule.attempt(context, plugin)];

                    case 6:
                        result = step.sent();
                        if (result instanceof contextModule.CoreContext) {
                            context = result
                        }
                        this.emit("message_enriched", context, plugin);

                    case 7:
                        i++;
                        return [3, 5];

                    case 8:
                        // 重新获取扩展（因为context可能已更新）
                        updatedExtensions = this.availableExtensions(null != (integrations = context.event.integrations) ? integrations : {});
                        destinationPlugins = updatedExtensions.destinations;
                        afterPlugins = updatedExtensions.after;

                        // 异步执行所有目标插件
                        return [4, new Promise(function(resolve, reject) {
                            setTimeout(function() {
                                var destinationPromises = destinationPlugins.map(function(plugin) {
                                    return pluginExecutorModule.attempt(context, plugin)
                                });
                                Promise.all(destinationPromises).then(resolve).catch(reject)
                            }, 0)
                        })];

                    case 9:
                        step.sent();
                        context.stats.increment("message_delivered");
                        this.emit("message_delivered", context);

                        // 执行after插件
                        afterPromises = afterPlugins.map(function(plugin) {
                            return pluginExecutorModule.attempt(context, plugin)
                        });
                        return [4, Promise.all(afterPromises)];

                    case 10:
                        step.sent();
                        return [2, context]
                }
            })
        })
    };

    exports.CoreEventQueue = CoreEventQueue
});

// ============================================================================
// 空模块占位符
// ============================================================================

// 空模块 - 用于占位
var emptyModule = moduleWrapper((exports, module) => {  // YSe -> emptyModule
    Object.defineProperty(exports, "__esModule", {
        value: true
    })
});

// ============================================================================
// 事件分发工具模块
// ============================================================================

// 事件分发工具 - 处理事件的分发和回调
var eventDispatcher = moduleWrapper((exports, module) => {  // $Se -> eventDispatcher
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.dispatch = exports.getDelay = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils),
        callbackModule = mne();  // 回调处理模块

    // 计算延迟时间
    exports.getDelay = function(startTime, timeout) {
        var elapsed = Date.now() - startTime;
        return Math.max((timeout ?? 300) - elapsed, 0)
    };

    // 分发事件到队列
    exports.dispatch = function(context, queue, emitter, options) {
        return commonJSUtils.__awaiter(this, void 0, void 0, function() {
            var startTime, result;

            return commonJSUtils.__generator(this, function(step) {
                switch (step.label) {
                    case 0:
                        emitter.emit("dispatch_start", context);
                        startTime = Date.now();

                        // 根据队列状态选择分发方式
                        if (queue.isEmpty()) {
                            return [4, queue.dispatchSingle(context)]
                        } else {
                            return [3, 2]
                        }

                    case 1:
                        result = step.sent();
                        return [3, 4];

                    case 2:
                        return [4, queue.dispatch(context)];

                    case 3:
                        result = step.sent();

                    case 4:
                        // 处理回调
                        if (null == options ? void 0 : options.callback) {
                            return [4, callbackModule.invokeCallback(result, options.callback, exports.getDelay(startTime, options.timeout))]
                        } else {
                            return [3, 6]
                        }

                    case 5:
                        result = step.sent();

                    case 6:
                        // 调试模式下刷新日志
                        if (null == options ? void 0 : options.debug) {
                            result.flush()
                        }

                        return [2, result]
                }
            })
        })
    }
});

// ============================================================================
// 对象方法绑定工具
// ============================================================================

// 绑定对象所有方法到实例
var methodBinder = moduleWrapper((exports, module) => {  // QSe -> methodBinder
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.bindAll = void 0;

    exports.bindAll = function(instance) {
        var prototype = instance.constructor.prototype,
            propertyNames = Object.getOwnPropertyNames(prototype);

        for (var i = 0; i < propertyNames.length; i++) {
            var propertyName = propertyNames[i],
                descriptor;

            if ("constructor" !== propertyName) {
                descriptor = Object.getOwnPropertyDescriptor(instance.constructor.prototype, propertyName);

                if (descriptor && "function" == typeof descriptor.value) {
                    instance[propertyName] = instance[propertyName].bind(instance)
                }
            }
        }

        return instance
    }
});

// ============================================================================
// 核心工具聚合模块
// ============================================================================

// 核心工具聚合导出 - 汇总所有核心工具
var coreUtilsAggregator = moduleWrapper((exports, module) => {  // zm -> coreUtilsAggregator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.CoreLogger = exports.backoff = void 0;

    commonJSHelpers();
    var commonJSUtils = markAsESModule(commonJSUtils);

    // 导出各种工具模块
    commonJSUtils.__exportStar(oSe(), exports);      // 对象工具
    commonJSUtils.__exportStar(lSe(), exports);      // 字符串工具
    commonJSUtils.__exportStar(une(), exports);      // 类型检查工具
    commonJSUtils.__exportStar(ySe(), exports);      // 数组工具
    commonJSUtils.__exportStar(mne(), exports);      // 回调工具
    commonJSUtils.__exportStar(priorityQueue(), exports);  // 优先级队列

    // 导出退避算法
    var backoffModule = backoffAlgorithm();
    Object.defineProperty(exports, "backoff", {
        enumerable: true,
        get: function() {
            return backoffModule.backoff
        }
    });

    // 导出其他核心模块
    commonJSUtils.__exportStar(coreContext(), exports);      // 核心上下文
    commonJSUtils.__exportStar(coreEventQueue(), exports);   // 核心事件队列
    commonJSUtils.__exportStar(emptyModule(), exports);      // 空模块
    commonJSUtils.__exportStar(eventDispatcher(), exports);  // 事件分发器
    commonJSUtils.__exportStar(fne(), exports);              // 其他工具
    commonJSUtils.__exportStar(pne(), exports);              // 其他工具
    commonJSUtils.__exportStar(gne(), exports);              // 其他工具
    commonJSUtils.__exportStar(methodBinder(), exports);     // 方法绑定器
    commonJSUtils.__exportStar(coreStats(), exports);        // 核心统计

    // 导出核心日志记录器
    var loggerModule = coreLogger();
    Object.defineProperty(exports, "CoreLogger", {
        enumerable: true,
        get: function() {
            return loggerModule.CoreLogger
        }
    });

    commonJSUtils.__exportStar(pluginExecutor(), exports)    // 插件执行器
});

// ============================================================================
// 设置验证模块
// ============================================================================

// 设置验证器 - 验证配置参数的有效性
var settingsValidator = moduleWrapper((exports, module) => {  // qSe -> settingsValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.validateSettings = void 0;

    var coreUtils = coreUtilsAggregator();

    exports.validateSettings = function(settings) {
        if (!settings.writeKey) {
            throw new coreUtils.ValidationError("writeKey", "writeKey is missing.")
        }
    }
});

// ============================================================================
// 版本信息模块
// ============================================================================

// 版本信息
var versionInfo = moduleWrapper((exports, module) => {  // Ine -> versionInfo
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.version = void 0;

    exports.version = "2.2.1"
});

// ============================================================================
// URL格式化工具
// ============================================================================

// URL格式化工具 - 创建格式化的URL
var urlFormatter = moduleWrapper((exports, module) => {  // USe -> urlFormatter
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.tryCreateFormattedUrl = void 0;

    exports.tryCreateFormattedUrl = function(baseUrl, path) {
        return new URL(path || "", baseUrl).href.replace(/\/$/, "")
    }
});

// ============================================================================
// UUID工具导出模块
// ============================================================================

// UUID工具导出 - 重新导出UUID生成功能
var uuidExporter = moduleWrapper((exports, module) => {  // zz -> uuidExporter
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.uuid = void 0;

    var uuidModule = uuidGenerator();

    Object.defineProperty(exports, "uuid", {
        enumerable: true,
        get: function() {
            return uuidModule.v4
        }
    })
});

// ============================================================================
// 上下文批处理模块
// ============================================================================

// 上下文批处理类 - 用于批量处理事件
var contextBatch = moduleWrapper((exports, module) => {  // GSe -> contextBatch
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.ContextBatch = void 0;

    var uuidExports = uuidExporter();

    // 上下文批处理类
    var ContextBatch = /** @class */ (function() {
        function ContextBatch(maxEventCount) {
            this.id = uuidExports.uuid();           // 批次ID
            this.items = [];                        // 批次项目
            this.sizeInBytes = 0;                   // 批次大小（字节）
            this.maxEventCount = Math.max(1, maxEventCount)  // 最大事件数量
        }

        // 尝试添加事件到批次
        ContextBatch.prototype.tryAdd = function(eventItem) {
            var eventSize;

            // 检查事件数量限制
            if (this.length === this.maxEventCount) {
                return {
                    success: false,
                    message: "Event limit of ".concat(this.maxEventCount, " has been exceeded.")
                }
            }

            // 计算事件大小
            eventSize = this.calculateSize(eventItem.context);

            // 检查单个事件大小限制（32KB）
            if (32768 < eventSize) {
                return {
                    success: false,
                    message: "Event exceeds maximum event size of 32 KB"
                }
            }

            // 检查批次总大小限制（480KB）
            if (491520 < this.sizeInBytes + eventSize) {
                return {
                    success: false,
                    message: "Event has caused batch size to exceed 480 KB"
                }
            }

            // 添加事件到批次
            this.items.push(eventItem);
            this.sizeInBytes += eventSize;

            return {
                success: true
            }
        };

        // 获取批次长度
        Object.defineProperty(ContextBatch.prototype, "length", {
            get: function() {
                return this.items.length
            },
            enumerable: false,
            configurable: true
        });

        // 计算事件大小
        ContextBatch.prototype.calculateSize = function(context) {
            return encodeURI(JSON.stringify(context.event)).split(/%..|i/).length
        };

        // 获取所有事件
        ContextBatch.prototype.getEvents = function() {
            return this.items.map(function(item) {
                return item.context.event
            })
        };

        // 获取所有上下文
        ContextBatch.prototype.getContexts = function() {
            return this.items.map(function(item) {
                return item.context
            })
        };

        // 解析所有事件（调用resolver）
        ContextBatch.prototype.resolveEvents = function() {
            this.items.forEach(function(item) {
                item.resolver(item.context)
            })
        };

        return ContextBatch
    }());

    exports.ContextBatch = ContextBatch
});

// ============================================================================
// 加密哈希模块
// ============================================================================

// 默认哈希函数 - 使用Node.js crypto模块
var defaultHasher = moduleWrapper((exports, module) => {  // Dne -> defaultHasher
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto");

    exports.default = function(algorithm, data) {
        return crypto.createHash(algorithm).update(data).digest()
    }
});

// ============================================================================
// 字节操作工具模块
// ============================================================================

// 字节操作工具 - 用于编码、解码和字节数组操作
var byteUtils = moduleWrapper((exports, module) => {  // Sa -> byteUtils
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decoder = exports.encoder = void 0;
    exports.concat = concatArrays;
    exports.p2s = passwordToSalt;
    exports.uint64be = uint64BigEndian;
    exports.uint32be = uint32BigEndian;
    exports.lengthAndInput = lengthAndInput;
    exports.concatKdf = concatKdf;

    var hasherModule = defaultHasher(),
        maxUint32 = 2 ** 32;

    exports.encoder = new TextEncoder();
    exports.decoder = new TextDecoder();

    // 连接多个Uint8Array
    function concatArrays() {
        var arrays = [];
        for (var i = 0; i < arguments.length; i++) {
            arrays[i] = arguments[i]
        }

        var totalLength = arrays.reduce(function(sum, arr) {
                return sum + arr.length
            }, 0),
            result = new Uint8Array(totalLength),
            offset = 0;

        for (var array of arrays) {
            result.set(array, offset);
            offset += array.length
        }

        return result
    }

    // 密码转盐值 (Password to Salt)
    function passwordToSalt(password, salt) {
        return concatArrays(exports.encoder.encode(password), new Uint8Array([0]), salt)
    }

    // 64位大端序
    function uint64BigEndian(value) {
        var high = Math.floor(value / maxUint32),
            low = value % maxUint32,
            result = new Uint8Array(8);

        writeUint32BE(result, high, 0);
        writeUint32BE(result, low, 4);
        return result
    }

    // 32位大端序
    function uint32BigEndian(value) {
        var result = new Uint8Array(4);
        writeUint32BE(result, value);
        return result
    }

    // 长度和输入数据组合
    function lengthAndInput(input) {
        return concatArrays(uint32BigEndian(input.length), input)
    }

    // 连接KDF (Key Derivation Function)
    async function concatKdf(sharedKey, keyLength, algorithmId) {
        var iterations = Math.ceil((keyLength >> 3) / 32),
            result = new Uint8Array(32 * iterations);

        for (let i = 0; i < iterations; i++) {
            var input = new Uint8Array(4 + sharedKey.length + algorithmId.length);
            input.set(uint32BigEndian(i + 1));
            input.set(sharedKey, 4);
            input.set(algorithmId, 4 + sharedKey.length);
            result.set(await hasherModule.default("sha256", input), 32 * i)
        }

        return result.slice(0, keyLength >> 3)
    }

    // 写入32位大端序整数
    function writeUint32BE(buffer, value, offset) {
        offset = offset || 0;
        if (value < 0 || maxUint32 <= value) {
            throw new RangeError("value must be >= 0 and <= ".concat(maxUint32 - 1, ". Received ") + value)
        }
        buffer.set([value >>> 24, value >>> 16, value >>> 8, 255 & value], offset)
    }
});

// ============================================================================
// Base64编码工具模块
// ============================================================================

// Base64编码解码工具
var base64Utils = moduleWrapper((exports, module) => {  // lu -> base64Utils
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.decode = exports.encode = exports.encodeBase64 = exports.decodeBase64 = void 0;

    var Buffer = require("buffer").Buffer,
        byteUtilsModule = byteUtils();

    // Base64URL编码
    exports.encode = function(data) {
        return Buffer.from(data).toString("base64url")
    };

    // Base64解码
    exports.decodeBase64 = function(data) {
        return new Uint8Array(Buffer.from(data, "base64"))
    };

    // Base64编码
    exports.encodeBase64 = function(data) {
        return Buffer.from(data).toString("base64")
    };

    // Base64URL解码
    exports.decode = function(data) {
        return new Uint8Array(Buffer.from(normalizeInput(data), "base64url"))
    };

    // 标准化输入数据
    function normalizeInput(input) {
        var normalized = input;
        if (normalized instanceof Uint8Array) {
            normalized = byteUtilsModule.decoder.decode(normalized)
        }
        return normalized
    }
});

// ============================================================================
// JOSE错误类模块
// ============================================================================

// JOSE (JSON Object Signing and Encryption) 错误类定义
var joseErrors = moduleWrapper((exports, module) => {  // Zr -> joseErrors
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.JWSSignatureVerificationFailed = exports.JWKSTimeout = exports.JWKSMultipleMatchingKeys =
    exports.JWKSNoMatchingKey = exports.JWKSInvalid = exports.JWKInvalid = exports.JWTInvalid =
    exports.JWSInvalid = exports.JWEInvalid = exports.JWEDecryptionFailed = exports.JOSENotSupported =
    exports.JOSEAlgNotAllowed = exports.JWTExpired = exports.JWTClaimValidationFailed = exports.JOSEError = void 0;

    // 基础JOSE错误类
    var JOSEError = /** @class */ (function(_super) {
        function JOSEError(message, options) {
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JOSE_GENERIC";
            _this.name = _this.constructor.name;
            if (Error.captureStackTrace) {
                Error.captureStackTrace(_this, _this.constructor)
            }
            return _this
        }
        JOSEError.code = "ERR_JOSE_GENERIC";
        return JOSEError
    }(Error));

    exports.JOSEError = JOSEError;

    // JWT声明验证失败错误
    var JWTClaimValidationFailed = /** @class */ (function(_super) {
        function JWTClaimValidationFailed(message, payload, claim, reason) {
            claim = void 0 === claim ? "unspecified" : claim;
            reason = void 0 === reason ? "unspecified" : reason;

            var _this = _super.call(this, message, {
                cause: {
                    claim: claim,
                    reason: reason,
                    payload: payload
                }
            }) || this;

            _this.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
            _this.claim = claim;
            _this.reason = reason;
            _this.payload = payload;
            return _this
        }
        JWTClaimValidationFailed.code = "ERR_JWT_CLAIM_VALIDATION_FAILED";
        return JWTClaimValidationFailed
    }(JOSEError));

    exports.JWTClaimValidationFailed = JWTClaimValidationFailed;

    // JWT过期错误
    var JWTExpired = /** @class */ (function(_super) {
        function JWTExpired(message, payload, claim, reason) {
            claim = void 0 === claim ? "unspecified" : claim;
            reason = void 0 === reason ? "unspecified" : reason;

            var _this = _super.call(this, message, {
                cause: {
                    claim: claim,
                    reason: reason,
                    payload: payload
                }
            }) || this;

            _this.code = "ERR_JWT_EXPIRED";
            _this.claim = claim;
            _this.reason = reason;
            _this.payload = payload;
            return _this
        }
        JWTExpired.code = "ERR_JWT_EXPIRED";
        return JWTExpired
    }(JOSEError));

    exports.JWTExpired = JWTExpired;

    // JOSE算法不允许错误
    var JOSEAlgNotAllowed = /** @class */ (function(_super) {
        function JOSEAlgNotAllowed() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JOSE_ALG_NOT_ALLOWED";
            return _this
        }
        JOSEAlgNotAllowed.code = "ERR_JOSE_ALG_NOT_ALLOWED";
        return JOSEAlgNotAllowed
    }(JOSEError));

    exports.JOSEAlgNotAllowed = JOSEAlgNotAllowed;

    // JOSE不支持错误
    var JOSENotSupported = /** @class */ (function(_super) {
        function JOSENotSupported() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JOSE_NOT_SUPPORTED";
            return _this
        }
        JOSENotSupported.code = "ERR_JOSE_NOT_SUPPORTED";
        return JOSENotSupported
    }(JOSEError));

    exports.JOSENotSupported = JOSENotSupported;

    // JWE解密失败错误
    var JWEDecryptionFailed = /** @class */ (function(_super) {
        function JWEDecryptionFailed(message, options) {
            message = void 0 === message ? "decryption operation failed" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWE_DECRYPTION_FAILED";
            return _this
        }
        JWEDecryptionFailed.code = "ERR_JWE_DECRYPTION_FAILED";
        return JWEDecryptionFailed
    }(JOSEError));

    exports.JWEDecryptionFailed = JWEDecryptionFailed;

    // JWE无效错误
    var JWEInvalid = /** @class */ (function(_super) {
        function JWEInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWE_INVALID";
            return _this
        }
        JWEInvalid.code = "ERR_JWE_INVALID";
        return JWEInvalid
    }(JOSEError));

    exports.JWEInvalid = JWEInvalid;

    // JWS无效错误
    var JWSInvalid = /** @class */ (function(_super) {
        function JWSInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWS_INVALID";
            return _this
        }
        JWSInvalid.code = "ERR_JWS_INVALID";
        return JWSInvalid
    }(JOSEError));

    exports.JWSInvalid = JWSInvalid;

    // JWT无效错误
    var JWTInvalid = /** @class */ (function(_super) {
        function JWTInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWT_INVALID";
            return _this
        }
        JWTInvalid.code = "ERR_JWT_INVALID";
        return JWTInvalid
    }(JOSEError));

    exports.JWTInvalid = JWTInvalid;

    // JWK无效错误
    var JWKInvalid = /** @class */ (function(_super) {
        function JWKInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWK_INVALID";
            return _this
        }
        JWKInvalid.code = "ERR_JWK_INVALID";
        return JWKInvalid
    }(JOSEError));

    exports.JWKInvalid = JWKInvalid;

    // JWKS无效错误
    var JWKSInvalid = /** @class */ (function(_super) {
        function JWKSInvalid() {
            var _this = _super !== null && _super.apply(this, arguments) || this;
            _this.code = "ERR_JWKS_INVALID";
            return _this
        }
        JWKSInvalid.code = "ERR_JWKS_INVALID";
        return JWKSInvalid
    }(JOSEError));

    exports.JWKSInvalid = JWKSInvalid;

    // JWKS无匹配密钥错误
    var JWKSNoMatchingKey = /** @class */ (function(_super) {
        function JWKSNoMatchingKey(message, options) {
            message = void 0 === message ? "no applicable key found in the JSON Web Key Set" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_NO_MATCHING_KEY";
            return _this
        }
        JWKSNoMatchingKey.code = "ERR_JWKS_NO_MATCHING_KEY";
        return JWKSNoMatchingKey
    }(JOSEError));

    exports.JWKSNoMatchingKey = JWKSNoMatchingKey;

    // JWKS多个匹配密钥错误
    var JWKSMultipleMatchingKeys = /** @class */ (function(_super) {
        function JWKSMultipleMatchingKeys(message, options) {
            message = void 0 === message ? "multiple matching keys found in the JSON Web Key Set" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
            return _this
        }
        JWKSMultipleMatchingKeys.code = "ERR_JWKS_MULTIPLE_MATCHING_KEYS";
        return JWKSMultipleMatchingKeys
    }(JOSEError));

    exports.JWKSMultipleMatchingKeys = JWKSMultipleMatchingKeys;

    // JWKS超时错误
    var JWKSTimeout = /** @class */ (function(_super) {
        function JWKSTimeout(message, options) {
            message = void 0 === message ? "request timed out" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWKS_TIMEOUT";
            return _this
        }
        JWKSTimeout.code = "ERR_JWKS_TIMEOUT";
        return JWKSTimeout
    }(JOSEError));

    exports.JWKSTimeout = JWKSTimeout;

    // JWS签名验证失败错误
    var JWSSignatureVerificationFailed = /** @class */ (function(_super) {
        function JWSSignatureVerificationFailed(message, options) {
            message = void 0 === message ? "signature verification failed" : message;
            var _this = _super.call(this, message, options) || this;
            _this.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";
            return _this
        }
        JWSSignatureVerificationFailed.code = "ERR_JWS_SIGNATURE_VERIFICATION_FAILED";
        return JWSSignatureVerificationFailed
    }(JOSEError));

    exports.JWSSignatureVerificationFailed = JWSSignatureVerificationFailed
});

// ============================================================================
// 随机数生成模块
// ============================================================================

// 随机数生成器 - 使用Node.js crypto模块
var randomGenerator = moduleWrapper((exports, module) => {  // wx -> randomGenerator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.default = void 0;

    var crypto = require("crypto");

    Object.defineProperty(exports, "default", {
        enumerable: true,
        get: function() {
            return crypto.randomFillSync
        }
    })
});

// ============================================================================
// 初始化向量生成模块
// ============================================================================

// 初始化向量生成器 - 用于加密算法
var ivGenerator = moduleWrapper((exports, module) => {  // jne -> ivGenerator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.bitLength = getBitLength;

    var joseErrorsModule = joseErrors(),
        randomModule = randomGenerator();

    // 获取算法对应的位长度
    function getBitLength(algorithm) {
        switch (algorithm) {
            case "A128GCM":
            case "A128GCMKW":
            case "A192GCM":
            case "A192GCMKW":
            case "A256GCM":
            case "A256GCMKW":
                return 96;
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                return 128;
            default:
                throw new joseErrorsModule.JOSENotSupported("Unsupported JWE Algorithm: " + algorithm)
        }
    }

    // 生成初始化向量
    exports.default = function(algorithm) {
        return randomModule.default(new Uint8Array(getBitLength(algorithm) >> 3))
    }
});

// ============================================================================
// 加密算法检查模块
// ============================================================================

// 加密算法检查器 - 验证算法支持性
var algorithmChecker = moduleWrapper((exports, module) => {  // Jne -> algorithmChecker
    Object.defineProperty(exports, "__esModule", {
        value: true
    })

    // 这个模块主要用于算法验证和检查
    // 具体实现会根据需要的加密算法进行扩展
});

// ============================================================================
// 初始化向量验证模块
// ============================================================================

// 初始化向量验证器 - 验证IV长度的正确性
var ivValidator = moduleWrapper((exports, module) => {  // 从2200行开始的模块
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var joseErrorsModule = joseErrors(),
        ivGeneratorModule = ivGenerator();

    exports.default = function(algorithm, iv) {
        if (iv.length << 3 !== ivGeneratorModule.bitLength(algorithm)) {
            throw new joseErrorsModule.JWEInvalid("Invalid Initialization Vector length")
        }
    }
});

// ============================================================================
// 密钥对象检测模块
// ============================================================================

// 密钥对象检测器 - 检测是否为Node.js KeyObject
var keyObjectDetector = moduleWrapper((exports, module) => {  // vp -> keyObjectDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var util = require("util");

    exports.default = function(obj) {
        return util.types.isKeyObject(obj)
    }
});

// ============================================================================
// 内容加密密钥验证模块
// ============================================================================

// 内容加密密钥验证器 - 验证CEK的长度和类型
var cekValidator = moduleWrapper((exports, module) => {  // tse -> cekValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var joseErrorsModule = joseErrors(),
        keyObjectModule = keyObjectDetector();

    exports.default = function(algorithm, cek) {
        var expectedBits;

        // 根据算法确定期望的密钥长度
        switch (algorithm) {
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                expectedBits = parseInt(algorithm.slice(-3), 10);
                break;
            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                expectedBits = parseInt(algorithm.slice(1, 4), 10);
                break;
            default:
                throw new joseErrorsModule.JOSENotSupported("Content Encryption Algorithm ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
        }

        if (cek instanceof Uint8Array) {
            // 验证Uint8Array密钥长度
            var actualBits = cek.byteLength << 3;
            if (actualBits !== expectedBits) {
                throw new joseErrorsModule.JWEInvalid("Invalid Content Encryption Key length. Expected ".concat(expectedBits, " bits, got ").concat(actualBits, " bits"))
            }
        } else {
            // 验证KeyObject密钥
            if (!keyObjectModule.default(cek) || "secret" !== cek.type) {
                throw new TypeError("Invalid Content Encryption Key type")
            }

            var actualBits = cek.symmetricKeySize << 3;
            if (actualBits !== expectedBits) {
                throw new joseErrorsModule.JWEInvalid("Invalid Content Encryption Key length. Expected ".concat(expectedBits, " bits, got ").concat(actualBits, " bits"))
            }
        }
    }
});

// ============================================================================
// 时间安全比较模块
// ============================================================================

// 时间安全比较器 - 防止时序攻击
var timingSafeEqual = moduleWrapper((exports, module) => {  // KSe -> timingSafeEqual
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto");

    exports.default = crypto.timingSafeEqual
});

// ============================================================================
// HMAC计算模块
// ============================================================================

// HMAC计算器 - 用于消息认证码计算
var hmacCalculator = moduleWrapper((exports, module) => {  // sse -> hmacCalculator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        byteUtilsModule = byteUtils();

    exports.default = function(associatedData, iv, ciphertext, bitLength, hmacKey, tagLength) {
        // 构建HMAC输入数据
        var hmacInput = byteUtilsModule.concat(
            associatedData,
            iv,
            ciphertext,
            byteUtilsModule.uint64be(associatedData.length << 3)
        );

        // 计算HMAC
        var hmac = crypto.createHmac("sha" + bitLength, hmacKey);
        hmac.update(hmacInput);

        return hmac.digest().slice(0, tagLength >> 3)
    }
});

// ============================================================================
// Web Crypto API模块
// ============================================================================

// Web Crypto API包装器 - 提供统一的加密接口
var webCrypto = moduleWrapper((exports, module) => {  // yp -> webCrypto
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isCryptoKey = void 0;

    var crypto = require("crypto"),
        util = require("util"),
        webcrypto = crypto.webcrypto;

    exports.default = webcrypto;

    exports.isCryptoKey = function(obj) {
        return util.types.isCryptoKey(obj)
    }
});

// ============================================================================
// 加密密钥验证模块
// ============================================================================

// 加密密钥验证器 - 验证CryptoKey是否支持特定操作
var cryptoKeyValidator = moduleWrapper((exports, module) => {  // Z3 -> cryptoKeyValidator
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.checkSigCryptoKey = checkSignatureCryptoKey;
    exports.checkEncCryptoKey = checkEncryptionCryptoKey;

    // 创建不支持操作的错误
    function createUnsupportedError(expected, property) {
        property = void 0 === property ? "algorithm.name" : property;
        return new TypeError("CryptoKey does not support this operation, its ".concat(property, " must be ") + expected)
    }

    // 检查算法名称是否匹配
    function isAlgorithmName(algorithm, name) {
        return algorithm.name === name
    }

    // 获取哈希算法的位数
    function getHashBits(hashAlgorithm) {
        return parseInt(hashAlgorithm.name.slice(4), 10)
    }

    // 检查密钥用途
    function checkKeyUsages(key, requiredUsages) {
        if (requiredUsages.length && !requiredUsages.some(function(usage) {
            return key.usages.includes(usage)
        })) {
            var errorMessage = "CryptoKey does not support this operation, its usages must include ";

            if (2 < requiredUsages.length) {
                var lastUsage = requiredUsages.pop();
                errorMessage += "one of ".concat(requiredUsages.join(", "), ", or ").concat(lastUsage, ".")
            } else if (2 === requiredUsages.length) {
                errorMessage += "one of ".concat(requiredUsages[0], " or ").concat(requiredUsages[1], ".")
            } else {
                errorMessage += requiredUsages[0] + "."
            }

            throw new TypeError(errorMessage)
        }
    }

    // 检查签名用CryptoKey
    function checkSignatureCryptoKey(key, algorithm) {
        var requiredUsages = [];
        for (var i = 2; i < arguments.length; i++) {
            requiredUsages[i - 2] = arguments[i]
        }

        switch (algorithm) {
            case "HS256":
            case "HS384":
            case "HS512":
                if (!isAlgorithmName(key.algorithm, "HMAC")) {
                    throw createUnsupportedError("HMAC")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "RS256":
            case "RS384":
            case "RS512":
                if (!isAlgorithmName(key.algorithm, "RSASSA-PKCS1-v1_5")) {
                    throw createUnsupportedError("RSASSA-PKCS1-v1_5")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "PS256":
            case "PS384":
            case "PS512":
                if (!isAlgorithmName(key.algorithm, "RSA-PSS")) {
                    throw createUnsupportedError("RSA-PSS")
                }
                var expectedBits = parseInt(algorithm.slice(2), 10);
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            case "EdDSA":
                if ("Ed25519" !== key.algorithm.name && "Ed448" !== key.algorithm.name) {
                    throw createUnsupportedError("Ed25519 or Ed448")
                }
                break;

            case "Ed25519":
                if (!isAlgorithmName(key.algorithm, "Ed25519")) {
                    throw createUnsupportedError("Ed25519")
                }
                break;

            case "ES256":
            case "ES384":
            case "ES512":
                if (!isAlgorithmName(key.algorithm, "ECDSA")) {
                    throw createUnsupportedError("ECDSA")
                }

                var expectedCurve = getExpectedCurve(algorithm);
                if (key.algorithm.namedCurve !== expectedCurve) {
                    throw createUnsupportedError(expectedCurve, "algorithm.namedCurve")
                }
                break;

            default:
                throw new TypeError("CryptoKey does not support this operation")
        }

        checkKeyUsages(key, requiredUsages)
    }

    // 获取期望的椭圆曲线
    function getExpectedCurve(algorithm) {
        switch (algorithm) {
            case "ES256":
                return "P-256";
            case "ES384":
                return "P-384";
            case "ES512":
                return "P-521";
            default:
                throw new Error("unreachable")
        }
    }

    // 检查加密用CryptoKey
    function checkEncryptionCryptoKey(key, algorithm) {
        var requiredUsages = [];
        for (var i = 2; i < arguments.length; i++) {
            requiredUsages[i - 2] = arguments[i]
        }

        switch (algorithm) {
            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                if (!isAlgorithmName(key.algorithm, "AES-GCM")) {
                    throw createUnsupportedError("AES-GCM")
                }
                var expectedLength = parseInt(algorithm.slice(1, 4), 10);
                if (key.algorithm.length !== expectedLength) {
                    throw createUnsupportedError(expectedLength, "algorithm.length")
                }
                break;

            case "A128KW":
            case "A192KW":
            case "A256KW":
                if (!isAlgorithmName(key.algorithm, "AES-KW")) {
                    throw createUnsupportedError("AES-KW")
                }
                var expectedLength = parseInt(algorithm.slice(1, 4), 10);
                if (key.algorithm.length !== expectedLength) {
                    throw createUnsupportedError(expectedLength, "algorithm.length")
                }
                break;

            case "ECDH":
                switch (key.algorithm.name) {
                    case "ECDH":
                    case "X25519":
                    case "X448":
                        break;
                    default:
                        throw createUnsupportedError("ECDH, X25519, or X448")
                }
                break;

            case "PBES2-HS256+A128KW":
            case "PBES2-HS384+A192KW":
            case "PBES2-HS512+A256KW":
                if (!isAlgorithmName(key.algorithm, "PBKDF2")) {
                    throw createUnsupportedError("PBKDF2")
                }
                break;

            case "RSA-OAEP":
            case "RSA-OAEP-256":
            case "RSA-OAEP-384":
            case "RSA-OAEP-512":
                if (!isAlgorithmName(key.algorithm, "RSA-OAEP")) {
                    throw createUnsupportedError("RSA-OAEP")
                }
                var expectedBits = parseInt(algorithm.slice(9), 10) || 1;
                if (getHashBits(key.algorithm.hash) !== expectedBits) {
                    throw createUnsupportedError("SHA-" + expectedBits, "algorithm.hash")
                }
                break;

            default:
                throw new TypeError("CryptoKey does not support this operation")
        }

        checkKeyUsages(key, requiredUsages)
    }
});

// ============================================================================
// 密钥类型错误处理模块
// ============================================================================

// 密钥类型错误处理器 - 生成详细的密钥类型错误信息
var keyTypeErrorHandler = moduleWrapper((exports, module) => {  // Ep -> keyTypeErrorHandler
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.withAlg = withAlgorithm;
    exports.default = createKeyTypeError;

    // 创建详细的错误信息
    function createDetailedErrorMessage(baseMessage, key) {
        var types = [];
        for (var i = 2; i < arguments.length; i++) {
            types[i - 2] = arguments[i]
        }

        // 过滤掉空值
        types = types.filter(Boolean);

        // 构建类型描述
        if (2 < types.length) {
            var lastType = types.pop();
            baseMessage += "one of type ".concat(types.join(", "), ", or ").concat(lastType, ".")
        } else if (2 === types.length) {
            baseMessage += "one of type ".concat(types[0], " or ").concat(types[1], ".")
        } else {
            baseMessage += "of type ".concat(types[0], ".")
        }

        // 添加实际接收到的类型信息
        if (null == key) {
            baseMessage += " Received " + key
        } else if ("function" == typeof key && key.name) {
            baseMessage += " Received function " + key.name
        } else if ("object" == typeof key && null != key && (null == key ? void 0 : key.constructor) && key.constructor.name) {
            baseMessage += " Received an instance of " + key.constructor.name
        }

        return baseMessage
    }

    // 为特定算法创建密钥错误信息
    function withAlgorithm(algorithm, key) {
        var types = [];
        for (var i = 2; i < arguments.length; i++) {
            types[i - 2] = arguments[i]
        }

        return createDetailedErrorMessage.apply(void 0,
            ["Key for the ".concat(algorithm, " algorithm must be "), key].concat(types))
    }

    // 创建通用密钥类型错误
    function createKeyTypeError(key) {
        var types = [];
        for (var i = 1; i < arguments.length; i++) {
            types[i - 1] = arguments[i]
        }

        return createDetailedErrorMessage.apply(void 0,
            ["Key must be "].concat([key], types))
    }
});

// ============================================================================
// 加密算法支持检测模块
// ============================================================================

// 加密算法支持检测器 - 检查Node.js是否支持特定加密算法
var cipherSupport = moduleWrapper((exports, module) => {  // Uz -> cipherSupport
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        supportedCiphers;

    exports.default = function(cipherName) {
        // 懒加载支持的加密算法列表
        if (!supportedCiphers) {
            supportedCiphers = new Set(crypto.getCiphers())
        }
        return supportedCiphers.has(cipherName)
    }
});

// ============================================================================
// 密钥类型检测模块
// ============================================================================

// 密钥类型检测器 - 检测各种密钥类型
var keyTypeDetector = moduleWrapper((exports, module) => {  // wp -> keyTypeDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.types = void 0;

    var webCryptoModule = webCrypto(),
        keyObjectModule = keyObjectDetector();

    // 检测是否为支持的密钥类型
    exports.default = function(key) {
        return keyObjectModule.default(key) || webCryptoModule.isCryptoKey(key)
    };

    // 支持的密钥类型列表
    var supportedTypes = ["KeyObject"];
    exports.types = supportedTypes;

    // 如果环境支持CryptoKey，添加到类型列表
    if (globalThis.CryptoKey || (null == webCryptoModule.default ? void 0 : webCryptoModule.default.CryptoKey)) {
        supportedTypes.push("CryptoKey")
    }
});

// ============================================================================
// JWE内容解密模块
// ============================================================================

// JWE内容解密器 - 解密JWE内容
var jweContentDecryptor = moduleWrapper((exports, module) => {  // cse -> jweContentDecryptor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    var crypto = require("crypto"),
        algorithmCheckerModule = algorithmChecker(),
        cekValidatorModule = cekValidator(),
        byteUtilsModule = byteUtils(),
        joseErrorsModule = joseErrors(),
        timingSafeEqualModule = timingSafeEqual(),
        hmacCalculatorModule = hmacCalculator(),
        webCryptoModule = webCrypto(),
        cryptoKeyValidatorModule = cryptoKeyValidator(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        cipherSupportModule = cipherSupport(),
        keyTypeModule = keyTypeDetector();

    exports.default = function(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var keyObject;

        // 处理CryptoKey类型的密钥
        if (webCryptoModule.isCryptoKey(cek)) {
            cryptoKeyValidatorModule.checkEncCryptoKey(cek, algorithm, "decrypt");
            keyObject = crypto.KeyObject.from(cek)
        } else {
            // 验证密钥类型
            if (!(cek instanceof Uint8Array || keyObjectModule.default(cek))) {
                throw new TypeError(keyTypeErrorModule.default.apply(void 0,
                    [cek].concat(keyTypeModule.types, ["Uint8Array"])))
            }
            keyObject = cek
        }

        // 验证必需的参数
        if (!iv) {
            throw new joseErrorsModule.JWEInvalid("JWE Initialization Vector missing")
        }
        if (!tag) {
            throw new joseErrorsModule.JWEInvalid("JWE Authentication Tag missing")
        }

        // 验证CEK和IV
        cekValidatorModule.default(algorithm, keyObject);
        algorithmCheckerModule.default(algorithm, iv);

        switch (algorithm) {
            case "A128CBC-HS256":
            case "A192CBC-HS384":
            case "A256CBC-HS512":
                return decryptCBCHS(algorithm, keyObject, ciphertext, iv, tag, additionalData);

            case "A128GCM":
            case "A192GCM":
            case "A256GCM":
                return decryptGCM(algorithm, keyObject, ciphertext, iv, tag, additionalData);

            default:
                throw new joseErrorsModule.JOSENotSupported("Unsupported JWE Content Encryption Algorithm")
        }
    };

    // 解密CBC+HMAC模式
    function decryptCBCHS(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var keySize = parseInt(algorithm.slice(1, 4), 10),
            exportedKey = keyObjectModule.default(cek) ? cek.export() : cek,
            encKey = exportedKey.subarray(keySize >> 3),
            macKey = exportedKey.subarray(0, keySize >> 3),
            hashSize = parseInt(algorithm.slice(-3), 10),
            cipherName = "aes-".concat(keySize, "-cbc");

        if (!cipherSupportModule.default(cipherName)) {
            throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported by your javascript runtime"))
        }

        // 验证HMAC标签
        var expectedTag = hmacCalculatorModule.default(additionalData, iv, ciphertext, hashSize, macKey, keySize),
            isValidTag;

        try {
            isValidTag = timingSafeEqualModule.default(tag, expectedTag)
        } catch (error) {
            // HMAC验证失败
        }

        if (isValidTag) {
            var plaintext;
            try {
                var decipher = crypto.createDecipheriv(cipherName, encKey, iv);
                plaintext = byteUtilsModule.concat(decipher.update(ciphertext), decipher.final())
            } catch (error) {
                // 解密失败
            }
            if (plaintext) {
                return plaintext
            }
        }

        throw new joseErrorsModule.JWEDecryptionFailed()
    }

    // 解密GCM模式
    function decryptGCM(algorithm, cek, ciphertext, iv, tag, additionalData) {
        var cipherName = "aes-".concat(parseInt(algorithm.slice(1, 4), 10), "-gcm");

        if (!cipherSupportModule.default(cipherName)) {
            throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported by your javascript runtime"))
        }

        try {
            var decipher = crypto.createDecipheriv(cipherName, cek, iv, {
                authTagLength: 16
            });

            decipher.setAuthTag(tag);

            if (additionalData.byteLength) {
                decipher.setAAD(additionalData, {
                    plaintextLength: ciphertext.length
                })
            }

            var plaintext = decipher.update(ciphertext);
            decipher.final();

            return plaintext
        } catch (error) {
            throw new joseErrorsModule.JWEDecryptionFailed()
        }
    }
});

// ============================================================================
// 对象属性检查模块
// ============================================================================

// 对象属性检查器 - 检查多个对象是否有重复属性
var objectPropertyChecker = moduleWrapper((exports, module) => {  // t6 -> objectPropertyChecker
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    exports.default = function() {
        var objects = [];
        for (var i = 0; i < arguments.length; i++) {
            objects[i] = arguments[i]
        }

        // 过滤掉空值
        objects = objects.filter(Boolean);

        if (0 === objects.length || 1 === objects.length) {
            return true
        }

        var allKeys;

        for (var obj of objects) {
            var keys = Object.keys(obj);

            if (allKeys && 0 !== allKeys.size) {
                // 检查是否有重复的键
                for (var key of keys) {
                    if (allKeys.has(key)) {
                        return false // 发现重复键
                    }
                    allKeys.add(key)
                }
            } else {
                allKeys = new Set(keys)
            }
        }

        return true
    }
});

// ============================================================================
// 纯对象检测模块
// ============================================================================

// 纯对象检测器 - 检测是否为纯JavaScript对象
var plainObjectDetector = moduleWrapper((exports, module) => {  // wl -> plainObjectDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });

    exports.default = function(obj) {
        // 检查是否为对象且不为null
        if (!isObject(obj) || "[object Object]" !== Object.prototype.toString.call(obj)) {
            return false
        }

        // 检查原型链
        if (null === Object.getPrototypeOf(obj)) {
            return true
        }

        var proto = obj;
        while (null !== Object.getPrototypeOf(proto)) {
            proto = Object.getPrototypeOf(proto)
        }

        return Object.getPrototypeOf(obj) === proto
    };

    // 辅助函数：检查是否为对象
    function isObject(value) {
        return "object" == typeof value && null !== value
    }
});

// ============================================================================
// AES密钥包装模块
// ============================================================================

// AES密钥包装器 - 实现AES-KW算法
var aesKeyWrapper = moduleWrapper((exports, module) => {  // Hz -> aesKeyWrapper
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.unwrap = unwrapKey;
    exports.wrap = wrapKey;

    var Buffer = require("buffer").Buffer,
        crypto = require("crypto"),
        joseErrorsModule = joseErrors(),
        byteUtilsModule = byteUtils(),
        webCryptoModule = webCrypto(),
        cryptoKeyValidatorModule = cryptoKeyValidator(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        cipherSupportModule = cipherSupport(),
        keyTypeModule = keyTypeDetector();

    // 验证密钥大小
    function validateKeySize(keyObject, algorithm) {
        if (keyObject.symmetricKeySize << 3 !== parseInt(algorithm.slice(1, 4), 10)) {
            throw new TypeError("Invalid key size for alg: " + algorithm)
        }
    }

    // 准备密钥对象
    function prepareKeyObject(key, algorithm, operation) {
        if (keyObjectModule.default(key)) {
            return key
        }
        if (key instanceof Uint8Array) {
            return crypto.createSecretKey(key)
        }
        if (webCryptoModule.isCryptoKey(key)) {
            cryptoKeyValidatorModule.checkEncCryptoKey(key, algorithm, operation);
            return crypto.KeyObject.from(key)
        }

        throw new TypeError(keyTypeErrorModule.default.apply(void 0,
            [key].concat(keyTypeModule.types, ["Uint8Array"])))
    }

    // 包装密钥
    function wrapKey(algorithm, kek, cek) {
        var cipherName = "aes".concat(parseInt(algorithm.slice(1, 4), 10), "-wrap");

        if (cipherSupportModule.default(cipherName)) {
            var keyObject = prepareKeyObject(kek, algorithm, "wrapKey");
            validateKeySize(keyObject, algorithm);

            var cipher = crypto.createCipheriv(cipherName, keyObject, Buffer.alloc(8, 166));
            return byteUtilsModule.concat(cipher.update(cek), cipher.final())
        }

        throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
    }

    // 解包密钥
    function unwrapKey(algorithm, kek, encryptedKey) {
        var cipherName = "aes".concat(parseInt(algorithm.slice(1, 4), 10), "-wrap");

        if (cipherSupportModule.default(cipherName)) {
            var keyObject = prepareKeyObject(kek, algorithm, "unwrapKey");
            validateKeySize(keyObject, algorithm);

            var decipher = crypto.createDecipheriv(cipherName, keyObject, Buffer.alloc(8, 166));
            return byteUtilsModule.concat(decipher.update(encryptedKey), decipher.final())
        }

        throw new joseErrorsModule.JOSENotSupported("alg ".concat(algorithm, " is not supported either by JOSE or your javascript runtime"))
    }
});

// ============================================================================
// JWK类型检测模块
// ============================================================================

// JWK类型检测器 - 检测各种JWK类型
var jwkTypeDetector = moduleWrapper((exports, module) => {  // xx -> jwkTypeDetector
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.isJWK = isJWK;
    exports.isPrivateJWK = isPrivateJWK;
    exports.isPublicJWK = isPublicJWK;
    exports.isSecretJWK = isSecretJWK;

    var plainObjectModule = plainObjectDetector();

    // 检测是否为JWK
    function isJWK(obj) {
        return plainObjectModule.default(obj) && "string" == typeof obj.kty
    }

    // 检测是否为私钥JWK
    function isPrivateJWK(jwk) {
        return "oct" !== jwk.kty && "string" == typeof jwk.d
    }

    // 检测是否为公钥JWK
    function isPublicJWK(jwk) {
        return "oct" !== jwk.kty && "undefined" == typeof jwk.d
    }

    // 检测是否为对称密钥JWK
    function isSecretJWK(jwk) {
        return isJWK(jwk) && "oct" === jwk.kty && "string" == typeof jwk.k
    }
});

// ============================================================================
// 密钥曲线提取模块
// ============================================================================

// 密钥曲线提取器 - 从密钥中提取椭圆曲线信息
var keyCurveExtractor = moduleWrapper((exports, module) => {  // dse -> keyCurveExtractor
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    exports.weakMap = void 0;

    var crypto = require("crypto"),
        joseErrorsModule = joseErrors(),
        webCryptoModule = webCrypto(),
        keyObjectModule = keyObjectDetector(),
        keyTypeErrorModule = keyTypeErrorHandler(),
        keyTypeModule = keyTypeDetector(),
        jwkTypeModule = jwkTypeDetector();

    // 弱映射缓存，用于存储密钥曲线信息
    exports.weakMap = new WeakMap();

    exports.default = function(key, allowedCurves) {
        var keyObject;

        if (webCryptoModule.isCryptoKey(key)) {
            keyObject = crypto.KeyObject.from(key)
        } else {
            if (!keyObjectModule.default(key)) {
                if (jwkTypeModule.isJWK(key)) {
                    return key.crv
                }
                throw new TypeError(keyTypeErrorModule.default.apply(void 0,
                    [key].concat(keyTypeModule.types)))
            }
            keyObject = key
        }

        // 从缓存中获取曲线信息
        var cachedCurve = exports.weakMap.get(keyObject);
        if (cachedCurve) {
            return cachedCurve
        }

        var curve;

        switch (keyObject.asymmetricKeyType) {
            case "ed25519":
            case "ed448":
                curve = "Ed25519" === keyObject.asymmetricKeyType ? "Ed25519" : "Ed448";
                break;

            case "x25519":
            case "x448":
                curve = "X25519" === keyObject.asymmetricKeyType ? "X25519" : "X448";
                break;

            case "ec":
                // 从密钥详情中提取椭圆曲线名称
                var keyDetails = keyObject.asymmetricKeyDetails;
                if (keyDetails && keyDetails.namedCurve) {
                    curve = keyDetails.namedCurve
                }
                break;

            default:
                throw new TypeError("Invalid key type for this operation")
        }

        if (!curve) {
            throw new TypeError("Unable to determine key curve")
        }

        // 验证曲线是否在允许列表中
        if (allowedCurves && !allowedCurves.includes(curve)) {
            throw new joseErrorsModule.JOSENotSupported("Unsupported curve: " + curve)
        }

        // 缓存结果
        exports.weakMap.set(keyObject, curve);

        return curve
    }
});
